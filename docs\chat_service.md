# 大模型文本交互服务使用文档

## 概述

大模型文本交互服务提供了与阿里云大模型进行文本对话的能力。通过简单的HTTP请求，您可以向大模型发送问题并获取回答。

## 接口说明

### 创建文本交互任务

#### 请求信息

- **接口地址**：`/api/v1/chat/new`
- **请求方式**：POST
- **Content-Type**：application/json

#### 请求参数

| 参数名 | 类型 | 是否必须 | 长度限制 | 描述 |
| ------ | ---- | -------- | -------- | ---- |
| Model | string | 否 | - | 使用的大模型名称，默认为"qwen-plus" |
| SysContent | string | 是 | 1-2000字符 | 系统角色的内容，用于设置大模型的行为和角色定位 |
| UserContent | string | 是 | 1-4000字符 | 用户角色的内容，即向大模型提出的问题或指令 |

**注意**: 本服务使用 **Pydantic** 进行请求数据验证，确保数据类型安全和格式正确。

#### 请求示例

```json
{
    "Model": "qwen-plus",
    "SysContent": "你是一个水平衡领域的AI助手",
    "UserContent": "请介绍一下节水措施的重要性"
}
```

#### 响应参数

服务直接返回大模型的回答内容。如果是JSON格式的回答，则会返回JSON字符串。

#### 响应示例

```
节水措施的重要性体现在多个方面：

1. 保护水资源：水是生命之源，也是不可再生资源。实施节水措施可以减少水资源浪费，保护有限的淡水资源。

2. 缓解水资源短缺：全球许多地区面临水资源短缺问题，节水措施可以帮助缓解供需矛盾，确保水资源的可持续利用。

3. 降低成本：对于企业和家庭来说，节水可以直接降低水费支出。对于需要处理大量用水的企业，节水措施可以显著降低运营成本。

4. 减少能源消耗：水的处理、输送和加热都需要能源，节约用水同时也节约了能源，减少了碳排放。

5. 减轻环境压力：减少用水量可以减少废水排放，降低对水环境的污染压力，保护水生态系统。

6. 提高水资源利用效率：通过技术创新和管理优化，节水措施可以提高单位水资源的使用效率，实现"用更少的水做更多的事"。

7. 应对气候变化：气候变化导致降水模式改变，增加了水资源的不确定性，节水措施有助于增强应对气候变化的韧性。

8. 促进可持续发展：水资源的可持续利用是实现经济社会可持续发展的重要保障，节水措施是水资源可持续利用的核心手段。

9. 提高社会意识：推广节水措施可以提高公众对水资源价值的认识，培养节约用水的良好习惯。

10. 法规遵从：许多地区已经制定了严格的用水管理法规，实施节水措施是遵守法律法规的需要。

总之，节水措施不仅关系到水资源的保护和利用，也关系到经济发展、环境保护和社会进步，具有重要的现实意义和长远价值。
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 400 | 请求参数验证失败 | 检查请求参数是否符合Pydantic验证规则 |
| 500 | 服务器内部错误 | 请联系管理员或稍后重试 |

### 常见验证错误示例

```json
{
  "Code": "400",
  "Message": "请求参数验证失败: SysContent: field required; UserContent: ensure this value has at least 1 characters",
  "Data": null,
  "RequestId": null
}
```

### 数据验证规则

- **SysContent**: 必需字段，长度1-2000字符，不能为空或仅包含空白字符
- **UserContent**: 必需字段，长度1-4000字符，不能为空或仅包含空白字符
- **Model**: 可选字段，字符串类型，默认值为"qwen-plus"

## 使用建议

1. **设置合适的系统提示语**：通过SysContent参数可以设置大模型的角色和行为，合理设置可以获得更符合预期的回答。

2. **明确的用户指令**：在UserContent中提供清晰、明确的指令或问题，可以帮助大模型更好地理解您的需求。

3. **选择合适的模型**：根据您的需求选择合适的大模型，不同的模型在不同任务上表现各异。

4. **错误处理**：在集成时，请务必处理可能出现的错误情况，确保应用的稳定性。

## 示例代码

### Python

```python
import requests
import json

url = "http://your-server-address/api/v1/chat/new"
headers = {"Content-Type": "application/json"}
data = {
    "Model": "qwen-plus",
    "SysContent": "你是一个水平衡领域的AI助手",
    "UserContent": "请介绍一下节水措施的重要性"
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.text)
```

### JavaScript

```javascript
fetch('http://your-server-address/api/v1/chat/new', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        Model: 'qwen-plus',
        SysContent: '你是一个水平衡领域的AI助手',
        UserContent: '请介绍一下节水措施的重要性'
    })
})
.then(response => response.text())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## 注意事项

1. 请确保您有足够的阿里云大模型API调用权限和配额。

2. 大模型的响应可能需要一定时间，请设置合理的超时时间。

3. 大模型的回答内容可能会随着模型的更新而变化，请做好应对措施。

4. 在处理敏感信息时，请注意数据安全和隐私保护。

5. 建议在生产环境中使用HTTPS协议，确保数据传输的安全性。
