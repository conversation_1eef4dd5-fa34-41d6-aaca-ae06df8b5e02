# aihub 测试文档

本文档介绍 aihub 项目的测试体系，包括测试结构、运行方法和测试内容说明。

## 测试体系结构

```
tests/
├── __init__.py              # 测试包初始化文件
├── conftest.py              # pytest全局配置和fixture
├── README.md                # 测试文档（本文件）
├── test_data/               # 测试数据目录
│   ├── __init__.py
│   └── content.html         # HTML测试数据
├── unit/                    # 单元测试
│   ├── __init__.py
│   └── test_*.py           # 单元测试文件
├── integration/             # 集成测试
│   ├── __init__.py
│   └── test_*.py           # 集成测试文件
└── api/                     # API测试
    ├── __init__.py
    └── test_docx_generation.py  # DOCX生成API测试
```

## 测试环境准备

### 1. 安装测试依赖

```bash
# 安装所有依赖（包括测试依赖）
pip install -r requirements.txt
```

### 2. 环境配置

确保以下环境配置正确：
- Python 3.8+
- 项目根目录在 Python 路径中
- 测试数据文件存在于 `tests/test_data/` 目录

## 运行测试

### 基本测试命令

```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest tests/unit/          # 单元测试
pytest tests/integration/   # 集成测试
pytest tests/api/          # API测试

# 运行特定测试文件
pytest tests/api/test_docx_generation.py

# 运行特定测试方法
pytest tests/api/test_docx_generation.py::TestDocxGenerationCore::test_parse_html_dom_basic
```

### 高级测试选项

```bash
# 详细输出模式
pytest -v

# 显示测试覆盖率
pytest --cov=app

# 生成HTML覆盖率报告
pytest --cov=app --cov-report=html

# 并行运行测试（需要pytest-xdist）
pytest -n auto

# 只运行失败的测试
pytest --lf

# 运行特定标记的测试
pytest -m unit      # 只运行单元测试
pytest -m api       # 只运行API测试
pytest -m "not slow"  # 跳过慢速测试
```

### 测试标记说明

- `@pytest.mark.unit`: 单元测试标记
- `@pytest.mark.integration`: 集成测试标记
- `@pytest.mark.api`: API测试标记
- `@pytest.mark.slow`: 运行时间较长的测试
- `@pytest.mark.skip_ci`: 在CI环境中跳过的测试

## 测试内容说明

### API测试 (`tests/api/test_docx_generation.py`)

#### TestDocxGenerationAPI 类
测试DOCX生成API接口的功能：

- **test_api_docx_generation_success**: 测试API成功生成DOCX文档
  - 发送完整的API请求
  - 验证响应结构和状态码
  - 下载并验证生成的文档文件
  - 检查文档内容完整性

- **test_api_request_validation**: 测试API请求参数验证
  - 测试空请求的处理
  - 验证错误响应格式

#### TestDocxGenerationCore 类
测试DOCX生成核心功能：

- **test_parse_html_dom_basic**: 测试parse_html_dom基本功能
  - 验证HTML到DOCX的基本转换
  - 检查生成文档的基本结构

- **test_parse_html_dom_no_empty_first_line**: 测试修复第一行空行问题
  - 验证生成的文档第一行不是空行
  - 确保修复方案有效

- **test_parse_html_dom_landscape_orientation**: 测试横向布局功能
  - 验证横向布局设置正确
  - 检查页面尺寸配置

- **test_document_structure_analysis**: 测试文档结构分析
  - 分析生成文档的段落结构
  - 验证表格等元素的正确转换

#### TestDocxGenerationEdgeCases 类
测试边界情况和异常处理：

- **test_empty_html_content**: 测试空HTML内容处理
- **test_invalid_html_content**: 测试无效HTML内容处理
- **test_multiple_content_blocks**: 测试多个内容块处理

### 单元测试 (`tests/unit/`)

目前主要包含现有的验证器测试：
- `test_validators.py`: 测试Pydantic数据验证器

### 集成测试 (`tests/integration/`)

用于测试多个模块间的交互，目前为预留结构。

## 测试数据

### content.html
位于 `tests/test_data/content.html`，包含：
- 水平衡测试表的完整HTML结构
- 多个表格和标题元素
- 用于测试HTML到DOCX转换的复杂内容

## 测试配置

### pytest.ini
项目根目录的pytest配置文件，包含：
- 测试发现规则
- 输出格式配置
- 覆盖率要求（70%）
- 测试标记定义

### conftest.py
全局测试配置文件，提供：
- 测试环境自动设置
- 共享的fixture（如测试数据路径、临时文件路径）
- 测试数据加载器

## 持续集成

### 本地测试检查清单

运行以下命令确保代码质量：

```bash
# 1. 运行所有测试
pytest

# 2. 检查代码覆盖率
pytest --cov=app --cov-fail-under=70

# 3. 代码格式检查
flake8 app/ tests/

# 4. 代码格式化
black app/ tests/
```

### CI/CD 集成建议

在CI/CD流水线中建议包含：
1. 依赖安装
2. 代码质量检查（flake8）
3. 测试运行（pytest）
4. 覆盖率检查
5. 测试报告生成

## 故障排除

### 常见问题

1. **ImportError: No module named 'app'**
   - 确保在项目根目录运行测试
   - 检查PYTHONPATH设置

2. **API测试失败**
   - 确保API服务器正在运行
   - 检查API地址配置
   - API不可用时会自动跳过相关测试

3. **测试数据不存在**
   - 确保 `tests/test_data/content.html` 文件存在
   - 相关测试会自动跳过

4. **覆盖率不足**
   - 添加更多测试用例
   - 检查未覆盖的代码分支

### 调试技巧

```bash
# 详细输出和调试信息
pytest -v -s

# 在第一个失败处停止
pytest -x

# 显示最慢的10个测试
pytest --durations=10

# 只运行包含特定关键词的测试
pytest -k "docx"
```

## 贡献指南

### 添加新测试

1. 根据测试类型选择合适的目录（unit/integration/api）
2. 创建以 `test_` 开头的测试文件
3. 使用适当的测试标记
4. 添加必要的文档字符串
5. 确保测试独立且可重复运行

### 测试命名规范

- 测试文件：`test_<module_name>.py`
- 测试类：`Test<FeatureName>`
- 测试方法：`test_<specific_behavior>`

### 最佳实践

1. 每个测试应该只测试一个特定功能
2. 使用描述性的测试名称
3. 适当使用fixture避免重复代码
4. 测试应该快速且独立
5. 添加适当的断言和错误消息

---

更多信息请参考 [pytest官方文档](https://docs.pytest.org/) 和项目的其他文档。