# -*- coding: utf-8 -*-
"""
工具函数模块

提供响应格式化、文件处理等辅助功能。
数据验证功能已迁移到validators.py模块，使用Pydantic进行类型安全的验证。
"""

import json
import aiohttp
import ijson
from typing import Dict, Any
from fastapi.responses import JSONResponse

# 导入日志记录器
from ..core.logger import get_logger
logger = get_logger()


# ========== HTTP 相关工具函数 ==========

async def safe_json_parse(response: aiohttp.ClientResponse) -> dict:
    """
    使用流式解析器读取 aiohttp 响应并返回完整的 JSON 对象。

    优点:
    1. 内存优化: 避免同时在内存中持有 '原始bytes' 和 '解析后的dict'。
    2. 快速响应: 数据一旦到达即开始解析，不需要等待下载完成。
    3. 完整性检查: 如果流中途断开，ijson 会抛出明确的 IncompleteJSONError。

    :param response: aiohttp.ClientResponse 对象
    :return: 解析后的 Python 对象 (通常是 dict 或 list)
    """
    try:
        # ijson.items 的前缀 '' (空字符串) 表示提取 JSON 的根对象
        # response.content 是一个异步的 StreamReader，ijson 原生支持
        async for root_obj in ijson.items(response.content, ''):
            return root_obj
    except ijson.IncompleteJSONError:
        logger.error("JSON 数据流被截断（下载不完整）")
        raise aiohttp.ClientPayloadError("Response payload incomplete")
    except ijson.JSONError as e:
        logger.error(f"JSON 格式错误: {e}")
        raise
    except Exception as e:
        logger.error(f"解析过程中发生未知错误: {e}")
        raise
    # 如果流结束了但没有解析出任何对象（例如空响应）
    return None


def get_optimized_timeout() -> aiohttp.ClientTimeout:
    """
    获取优化的超时配置，适用于可能返回大型响应的API调用

    Returns:
        aiohttp.ClientTimeout: 配置了较长超时时间的超时对象
    """
    # 为大型响应设置更长的超时时间
    # total: 总超时时间（包括连接和读取）
    # connect: 连接超时
    # sock_read: 单次socket读取超时
    return aiohttp.ClientTimeout(
        total=1200,     # 总共20分钟
        connect=30,     # 连接30秒
        sock_read=300   # 读取5分钟
    )


# ========== 响应处理工具函数 ==========


def create_error_response(message: str, status_code: int = 400, data: Any = None) -> JSONResponse:
    """
    创建标准化的错误响应

    用于生成统一格式的API错误响应，包含错误码、错误信息等标准字段。

    Args:
        message (str): 错误信息描述
        status_code (int): HTTP状态码，默认为400（客户端错误）
        data (Any): 附加数据，默认为None

    Returns:
        JSONResponse: FastAPI JSON响应对象，包含标准化的错误信息格式

    Example:
        >>> error_response = create_error_response("参数验证失败", 400, data={"field": "error"})
        >>> # 返回: {"Code": 400, "Message": "参数验证失败", "Data": {"field": "error"}, "RequestId": None}
    """
    response = {
        "Code": status_code,
        "Message": message,
        "Data": data,
        "RequestId": None
    }
    return JSONResponse(content=response, status_code=status_code)


def create_success_response(data: Dict[str, Any], message: str = "success") -> Dict[str, Any]:
    """
    创建标准化的成功响应

    用于生成统一格式的API成功响应，包含成功码、数据内容等标准字段。

    Args:
        data (Dict[str, Any]): 响应数据内容，通常包含API调用的结果
        message (str): 成功信息描述，默认为"success"

    Returns:
        Dict[str, Any]: 标准化的响应数据字典

    Example:
        >>> success_data = {"TaskId": "12345", "Status": "Created"}
        >>> response = create_success_response(success_data, "任务创建成功")
        >>> # 返回: {"Code": 0, "Message": "任务创建成功", "Data": {...}, "RequestId": "12345"}
    """
    response = {
        "Code": 0,
        "Message": message,
        "Data": data,
        "RequestId": data.get("RequestId") if isinstance(data, dict) else None
    }
    return response


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示

    将字节数转换为人类可读的文件大小格式（B, KB, MB, GB, TB）。

    Args:
        size_bytes (int): 文件大小（以字节为单位）

    Returns:
        str: 格式化后的文件大小字符串，保留一位小数

    Example:
        >>> format_file_size(1024)
        '1.0KB'
        >>> format_file_size(1536)
        '1.5KB'
        >>> format_file_size(0)
        '0B'
    """
    if size_bytes == 0:
        return "0B"

    # 定义文件大小单位
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0

    # 循环除以1024直到找到合适的单位
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符

    移除文件名中的路径分隔符和其他可能导致安全问题的字符，
    确保文件名可以安全地在文件系统中使用。

    Args:
        filename (str): 原始文件名

    Returns:
        str: 清理后的安全文件名

    Example:
        >>> sanitize_filename("test/file<name>.txt")
        'test_file_name_.txt'
        >>> sanitize_filename("very_long_filename" * 20 + ".txt")
        # 返回截断后的文件名，保持在255字符以内
    """
    # 定义不安全的字符列表
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']

    # 将不安全字符替换为下划线
    for char in unsafe_chars:
        filename = filename.replace(char, '_')

    # 限制文件名长度（大多数文件系统的限制是255字符）
    if len(filename) > 255:
        # 尝试分离文件名和扩展名
        if '.' in filename:
            name, ext = filename.rsplit('.', 1)
            # 计算允许的文件名长度（减去扩展名和点的长度）
            max_name_length = 255 - len(ext) - 1
            filename = name[:max_name_length] + '.' + ext
        else:
            # 没有扩展名的情况
            filename = filename[:255]

    return filename


def extract_audio_info_from_url(url: str) -> Dict[str, Any]:
    """
    从URL中提取音频文件信息

    解析音频文件URL，提取文件名、格式、域名等信息，
    支持多种常见的音频和视频格式。

    Args:
        url (str): 音频文件的完整URL地址

    Returns:
        Dict[str, Any]: 包含音频文件详细信息的字典，包含以下字段：
            - filename: 完整文件名
            - name: 不含扩展名的文件名
            - extension: 文件扩展名
            - format: 文件格式类型
            - url: 原始URL
            - domain: 域名

    Example:
        >>> info = extract_audio_info_from_url("https://example.com/audio/sample.mp3")
        >>> print(info)
        {
            'filename': 'sample.mp3',
            'name': 'sample',
            'extension': '.mp3',
            'format': 'MP3',
            'url': 'https://example.com/audio/sample.mp3',
            'domain': 'example.com'
        }
    """
    import os
    from urllib.parse import urlparse, unquote

    # 解析URL
    parsed_url = urlparse(url)

    # 从URL路径中提取文件名，并进行URL解码
    filename = os.path.basename(unquote(parsed_url.path))

    # 分离文件名和扩展名
    if '.' in filename:
        name, ext = os.path.splitext(filename)
    else:
        name, ext = filename, ''

    # 支持的音频和视频格式映射表
    audio_formats = {
        # 音频格式
        '.mp3': 'MP3',
        '.wav': 'WAV',
        '.m4a': 'M4A',
        '.aac': 'AAC',
        '.flac': 'FLAC',
        '.ogg': 'OGG',
        '.wma': 'WMA',
        '.opus': 'OPUS',
        '.aiff': 'AIFF',
        '.au': 'AU',

        # 视频格式（通常包含音频轨道）
        '.mp4': 'MP4',
        '.avi': 'AVI',
        '.mov': 'MOV',
        '.mkv': 'MKV',
        '.wmv': 'WMV',
        '.flv': 'FLV',
        '.webm': 'WEBM',
        '.3gp': '3GP'
    }

    # 获取文件格式，不区分大小写
    file_format = audio_formats.get(ext.lower(), 'Unknown')

    return {
        'filename': filename,
        'name': name,
        'extension': ext,
        'format': file_format,
        'url': url,
        'domain': parsed_url.netloc
    }
