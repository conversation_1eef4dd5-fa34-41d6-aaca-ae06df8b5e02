# -*- coding: utf-8 -*-
"""
Coze文档处理API路由

处理Coze文档处理相关的API请求。
"""

import asyncio
from fastapi import APIRouter, Request
from pydantic import BaseModel

# 导入项目内部模块
from ..services.coze_service import handle_coze_doc_request
from ..core.logger import get_logger
from ..models.requests import CozeDocRequest
from ..models.responses import CozeDocResponse

# 创建路由器
router = APIRouter(
    prefix="/api/v1/coze",
    tags=["COZE"],
    responses={404: {"description": "Not found"}}
)

# 获取日志记录器
logger = get_logger()


@router.post('/doc/new')
async def create_coze_doc_task(request: CozeDocRequest):
    """
    创建Coze文档处理任务端点.

    接收POST请求，创建Coze文档处理任务。

    请求体格式:
    {
        "TaskKey": 字符串, 任务惟一标识符, JSON字符串: "d1fg7skT"
        "Callback":  字符串, 回调URL, JSON字符串: "http://127.0.0.1:5577/api/v1/coze/doc/callback"
        "Contents": 数组, 每个元素都是一个文件的uri, JSON字符串: "[\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689405525.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689405714.jpg\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689407646.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689408376.jpg\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689408565.pdf\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689412526.docx\"]"
    }

    Contents: 数组, 每个元素是一个字符串, 字符串是文件的uri, 例如: "/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx"
    每个文件的uri都指向一个远程文件, 文件格式包括图像文件, 文档文件, 表格文件, pdf文件等格式.
    文件的完整http地址为所接收的http请求的主机地址+端口+uri: 例如http://127.0.0.1:5577/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx


    请求体完整例子(JSON str):
    "{\"TaskKey\":\"COZE_TEST_KEY_1\",\"Callback\":\"http://127.0.0.1:5577/api/v1/coze/doc/callback\",\"Contents\":[\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689404983.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689405525.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689405714.jpg\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689407646.docx\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689408376.jpg\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689408565.pdf\",\"/assets/proj/2025/03/phk5rp9cahskqltlv8/fattach/PHK5R1756689412526.docx\"]}"

    Returns:
        JSON响应: 任务创建结果
    {
        "TaskKey": "d1fg7skT",       // 任务惟一标识符
        "RuntimeStatus": "SUCCESS",  // 程序运行状态, "SUCCESS", "FAILED"
        "TaskStatus": "CREATED",     // 任务状态: 'CREATED', 'COMPLETED', 'ONGOING'
    }

    注一: 这个路由使用同步函数, FastAPI会将任务放到线程池中执行, 避免阻塞事件循环.

    注二: 这里使用Pydantic模型和依赖注入来处理请求数据验证和转换, 避免在同步函数中调用`request.json()`这个异步方法.
        **FastAPI会自动完成以下工作**:
        1. 异步地读取请求body
        2. 解析JSON
        3. 验证数据是否符合Item模型的结构和类型
        4. 如果验证失败，自动返回一个详细的422错误响应
        5. 将验证通过的数据转换成一个DocxgenRequest对象，注入到你的函数中

        **这种方法的步骤是**:
        1. 定义一个Pydantic模型, 用于定义请求体的结构和数据类型.
        2. 在路由函数中, 使用该模型作为参数, FastAPI会自动从请求体中提取数据并进行验证.
        3. 如果验证成功, 则将数据转换为模型实例, 并将其作为参数传递给处理函数.
        4. 如果验证失败, 则FastAPI会自动返回422 Unprocessable Entity响应, 并包含验证错误信息.

    """
    logger.info(f"创建Coze文档处理任务, TaskKey: {request.TaskKey}")

    # 构建请求体
    request_body = {"TaskKey": request.TaskKey, "ProjType": request.ProjType, "Callback": request.Callback, "Contents": request.Contents}

    # 在后台异步执行handle_coze_doc_request，不等待结果
    asyncio.create_task(handle_coze_doc_request(request_body))

    # 立即返回任务创建成功的响应
    return CozeDocResponse(
        Code=0,
        Message="任务已创建，正在后台处理",
        TaskKey=request.TaskKey,
        RuntimeStatus="SUCCESS",
        TaskStatus="CREATED",
        Data={},
    )
