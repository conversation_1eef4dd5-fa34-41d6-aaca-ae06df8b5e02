# -*- coding: utf-8 -*-
"""
pytest配置文件

包含全局测试配置和共享的fixture
"""

import os
import sys
import pytest
from pathlib import Path

# 将项目根目录添加到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试数据目录
TEST_DATA_DIR = Path(__file__).parent / "test_data"


@pytest.fixture(scope="session")
def test_data_dir():
    """返回测试数据目录路径"""
    return TEST_DATA_DIR


@pytest.fixture(scope="session")
def sample_html_content():
    """读取示例HTML内容"""
    html_file = TEST_DATA_DIR / "content.html"
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            return f.read()
    return None


@pytest.fixture(scope="function")
def temp_docx_path(tmp_path):
    """提供临时DOCX文件路径"""
    return tmp_path / "test_output.docx"


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 确保测试数据目录存在
    TEST_DATA_DIR.mkdir(exist_ok=True)
    
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    
    yield
    
    # 清理测试环境
    if "TESTING" in os.environ:
        del os.environ["TESTING"]