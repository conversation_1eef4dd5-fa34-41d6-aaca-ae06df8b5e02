#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coze服务集成测试

测试流程:
1. 上传测试文件到Coze平台
2. 调用智能体进行对话
3. 轮询对话状态直到完成

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.coze_service import upload_file, call_chat_agent, check_conversion_completed


class CozeServiceIntegrationTest:
    """Coze服务集成测试类"""
    
    def __init__(self):
        """初始化测试参数"""
        # 测试参数配置
        self.token = "pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo"
        self.bot_id = "7541317373962747945"
        self.user_id = "lcmp123456789"
        
        # 测试文件路径
        self.test_data_dir = project_root / "tests" / "test_data"
        self.test_files = [
            self.test_data_dir / "简介.docx",
            self.test_data_dir / "人数和水量.jpg"
        ]
        
        # 验证测试文件是否存在
        for file_path in self.test_files:
            if not file_path.exists():
                raise FileNotFoundError(f"测试文件不存在: {file_path}")
    
    def success_callback(self):
        """对话完成成功回调函数"""
        print("✅ 对话已成功完成！")
        print("📝 智能体已处理完所有上传的文件")
    
    def error_callback(self, exception: Exception):
        """对话出错回调函数"""
        print(f"❌ 对话过程中发生错误: {type(exception).__name__}")
        print(f"📄 错误详情: {str(exception)}")
    
    async def upload_test_files(self) -> List[Dict[str, str]]:
        """上传测试文件并返回文件列表
        
        Returns:
            List[Dict[str, str]]: 包含文件类型和ID的字典列表
        """
        print("📤 开始上传测试文件...")
        files_list = []
        
        for i, file_path in enumerate(self.test_files, 1):
            print(f"📁 正在上传第{i}个文件: {file_path.name}")
            
            try:
                # 调用upload_file函数上传文件
                file_result = await upload_file(
                    token=self.token,
                    file_path=str(file_path)
                )
                
                # 构建文件信息字典
                file_info = {
                    "type": "file",
                    "file_id": file_result
                }
                files_list.append(file_info)
                
                print(f"✅ 文件上传成功: {file_path.name}")
                print(f"📋 文件ID: {file_result}")
                
            except Exception as e:
                print(f"❌ 文件上传失败: {file_path.name}")
                print(f"📄 错误信息: {str(e)}")
                raise
        
        print(f"🎉 所有文件上传完成，共上传 {len(files_list)} 个文件")
        return files_list
    
    async def call_chat_agent_with_files(self, files_list: List[Dict[str, str]]) -> Dict[str, str]:
        """调用智能体进行对话
        
        Args:
            files_list: 文件列表
            
        Returns:
            Dict[str, str]: 包含conversation_id和chat_id的字典
        """
        print("🤖 开始调用智能体...")
        
        try:
            # 调用call_chat_agent函数
            chat_result = await call_chat_agent(
                token=self.token,
                bot_id=self.bot_id,
                user_id=self.user_id,
                files_list=files_list
            )
            
            print("✅ 智能体调用成功")
            print(f"📋 对话ID: {chat_result.get('conversation_id')}")
            print(f"📋 聊天ID: {chat_result.get('id')}")
            
            return chat_result
            
        except Exception as e:
            print(f"❌ 智能体调用失败: {str(e)}")
            raise
    
    async def monitor_conversation_status(self, conversation_id: str, chat_id: str):
        """监控对话状态直到完成
        
        Args:
            conversation_id: 对话ID
            chat_id: 聊天ID
        """
        print("⏳ 开始监控对话状态...")
        print(f"📋 对话ID: {conversation_id}")
        print(f"📋 聊天ID: {chat_id}")
        
        try:
            # 调用check_conversion_completed函数轮询状态
            await check_conversion_completed(
                token=self.token,
                conversation_id=conversation_id,
                chat_id=chat_id,
                interval=10,  # 每10秒查询一次
                timeout=1200,  # 总超时时间20分钟
                callback=self.success_callback,
                errback=self.error_callback
            )
            
        except Exception as e:
            print(f"❌ 状态监控过程中发生异常: {str(e)}")
            raise
    
    async def run_integration_test(self):
        """运行完整的集成测试"""
        print("🚀 开始Coze服务集成测试")
        print("=" * 50)
        
        try:
            # 步骤1: 上传文件
            print("\n📤 步骤1: 上传测试文件")
            files_list = await self.upload_test_files()
            
            # 步骤2: 调用智能体
            print("\n🤖 步骤2: 调用智能体")
            chat_result = await self.call_chat_agent_with_files(files_list)
            
            # 步骤3: 监控对话状态
            print("\n⏳ 步骤3: 监控对话状态")
            await self.monitor_conversation_status(
                conversation_id=chat_result.get('conversation_id'),
                chat_id=chat_result.get('id')
            )
            
            print("\n🎉 集成测试完成！")
            print("=" * 50)
            
        except Exception as e:
            print(f"\n❌ 集成测试失败: {str(e)}")
            print("=" * 50)
            raise


async def main():
    """主函数"""
    try:
        # 创建测试实例
        test_instance = CozeServiceIntegrationTest()
        
        # 运行集成测试
        await test_instance.run_integration_test()
        
    except Exception as e:
        print(f"测试执行失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    # 运行异步主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)