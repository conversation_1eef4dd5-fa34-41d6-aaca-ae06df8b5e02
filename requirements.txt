# FastAPI Web框架 + ASGI服务器（替换Flask + Waitress以获得更高性能）
fastapi>=0.116.1
uvicorn[standard]>=0.35.0
gunicorn>=21.2.0
setproctitle>=1.3.6
pydantic>=2.11.0
ijson

# Gen doc
html-for-docx
python-docx >= 1.2.0
beautifulsoup4 >= 4.13.4
latex2mathml >= 1.1.1
lxml >= 6.0.0
Pillow >= 11.3.0
PyMuPDF >= 1.26.4

# 阿里云千义听悟SDK依赖
alibabacloud_tingwu20230930>=2.0.23,<3.0.0
alibabacloud_tea_openapi>=0.3.15,<1.0.0
alibabacloud_tea_console>=0.0.1,<1.0.0
alibabacloud_tea_util>=0.3.13,<1.0.0
alibabacloud_openapi_util>=0.2.2,<1.0.0
alibabacloud_credentials>=1.0.2,<2.0.0

# 阿里云文本大模型SDK
dashscope>=1.10.0

# 其他工具库
requests==2.31.0
urllib3==2.1.0

# 开发和测试工具
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-xdist>=3.3.1
pytest-mock>=3.11.1
flake8>=6.1.0
black>=23.11.0
coverage>=7.3.0

# 测试辅助工具
factory-boy>=3.3.0
faker>=19.12.0
responses>=0.23.3
