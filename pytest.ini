[tool:pytest]
# pytest配置文件

# 测试发现配置
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=70

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 运行时间较长的测试
    skip_ci: 在CI环境中跳过的测试

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning