#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解析功能使用示例

本示例展示如何使用parse_html_dom函数将HTML内容转换为DOCX文档。
该功能已集成到utils.py的parse_simple_element函数中。
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from docx import Document
from app.gendoc.utils import parse_simple_element

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本HTML解析示例 ===")
    
    # 创建新文档
    doc = Document()
    
    # HTML内容配置
    html_config = {
        "type": "HTML",
        "content": """
        <div>
            <h1>项目报告</h1>
            <p>这是项目的基本信息介绍。</p>
            <h2>主要功能</h2>
            <ul>
                <li>HTML到DOCX转换</li>
                <li>支持多种HTML标签</li>
                <li>自定义样式设置</li>
            </ul>
        </div>
        """,
        "fontName": "SimSun",
        "fontSize": 12,
        "fontBold": False,
        "fontItalic": False,
        "align": "Left",
        "leftIndent": 0,
        "lineSpacing": 1.0,
        "spaceBefore": 0,
        "spaceAfter": 0
    }
    
    # 使用parse_simple_element函数解析HTML
    doc = parse_simple_element(doc, html_config)
    
    # 保存文档
    doc.save("example_basic_usage.docx")
    print("基本使用示例完成，文档已保存为 example_basic_usage.docx")

def example_styled_content():
    """带样式的HTML内容示例"""
    print("=== 带样式HTML解析示例 ===")
    
    # 创建新文档
    doc = Document()
    
    # 带样式的HTML内容配置
    styled_html_config = {
        "type": "HTML",
        "content": """
        <div>
            <h1>重要通知</h1>
            <p>请所有员工注意以下重要事项：</p>
            <ol>
                <li>会议时间调整为下午3点</li>
                <li>请提前准备相关材料</li>
                <li>会议室变更为A101</li>
            </ol>
            <h2>联系方式</h2>
            <p>如有疑问，请联系人事部门。</p>
        </div>
        """,
        "fontName": "SimHei",
        "fontSize": 14,
        "fontBold": True,
        "fontItalic": False,
        "align": "Center",
        "leftIndent": 1,
        "lineSpacing": 1.5,
        "spaceBefore": 0.3,
        "spaceAfter": 0.3
    }
    
    # 使用parse_simple_element函数解析HTML
    doc = parse_simple_element(doc, styled_html_config)
    
    # 保存文档
    doc.save("example_styled_content.docx")
    print("带样式示例完成，文档已保存为 example_styled_content.docx")

def example_table_content():
    """包含表格的HTML内容示例"""
    print("=== 表格HTML解析示例 ===")
    
    # 创建新文档
    doc = Document()
    
    # 包含表格的HTML内容配置
    table_html_config = {
        "type": "HTML",
        "content": """
        <div>
            <h2>销售数据统计</h2>
            <p>以下是本月各产品的销售情况：</p>
            <table border="1">
                <tr>
                    <th colspan="2">产品信息</th>
                    <th>销售数据</th>
                </tr>
                <tr>
                    <th>产品名称</th>
                    <th>产品代码</th>
                    <th>销售量</th>
                </tr>
                <tr>
                    <td>智能手机</td>
                    <td>SP001</td>
                    <td>1500台</td>
                </tr>
                <tr>
                    <td>平板电脑</td>
                    <td>TB002</td>
                    <td>800台</td>
                </tr>
                <tr>
                    <td rowspan="2">配件类</td>
                    <td>AC003</td>
                    <td>2000个</td>
                </tr>
                <tr>
                    <td>AC004</td>
                    <td>1200个</td>
                </tr>
            </table>
            <p>总体销售情况良好，超出预期目标。</p>
        </div>
        """,
        "fontName": "SimSun",
        "fontSize": 11,
        "fontBold": False,
        "fontItalic": False,
        "align": "Left",
        "leftIndent": 0,
        "lineSpacing": 1.2,
        "spaceBefore": 0.2,
        "spaceAfter": 0.2
    }
    
    # 使用parse_simple_element函数解析HTML
    doc = parse_simple_element(doc, table_html_config)
    
    # 保存文档
    doc.save("example_table_content.docx")
    print("表格示例完成，文档已保存为 example_table_content.docx")

def example_mixed_document():
    """混合文档示例 - 结合其他元素类型"""
    print("=== 混合文档示例 ===")
    
    # 创建新文档
    doc = Document()
    
    # 添加标题
    title_config = {
        "type": "Title",
        "content": "技术文档",
        "fontName": "SimHei",
        "fontSize": 24,
        "fontBold": True,
        "align": "Center"
    }
    doc = parse_simple_element(doc, title_config)
    
    # 添加HTML内容
    html_config = {
        "type": "HTML",
        "content": """
        <div>
            <h2>系统架构</h2>
            <p>本系统采用模块化设计，主要包含以下组件：</p>
            <ul>
                <li>前端展示层</li>
                <li>业务逻辑层</li>
                <li>数据访问层</li>
            </ul>
            <h3>技术栈</h3>
            <p>使用的主要技术包括：</p>
            <ol>
                <li>Python 3.8+</li>
                <li>python-docx库</li>
                <li>BeautifulSoup4</li>
            </ol>
        </div>
        """,
        "fontName": "SimSun",
        "fontSize": 12,
        "align": "Left"
    }
    doc = parse_simple_element(doc, html_config)
    
    # 添加简单段落
    para_config = {
        "type": "SimPara",
        "content": "以上就是系统的基本架构介绍，更多详细信息请参考技术规范文档。",
        "fontName": "SimSun",
        "fontSize": 10.5,
        "align": "Left"
    }
    doc = parse_simple_element(doc, para_config)
    
    # 保存文档
    doc.save("example_mixed_document.docx")
    print("混合文档示例完成，文档已保存为 example_mixed_document.docx")

def example_json_format():
    """JSON格式示例 - 展示实际使用中的JSON字符串格式"""
    print("=== JSON格式示例 ===")
    
    import json
    
    # 模拟从JSON字符串解析的情况
    json_string = '''{
        "type": "HTML",
        "content": "<div>\\n  <h1>JSON格式示例</h1>\\n  <p>这是从JSON字符串解析的HTML内容。</p>\\n  <ul>\\n    <li>支持转义字符</li>\\n    <li>支持换行符</li>\\n  </ul>\\n</div>",
        "fontName": "SimSun",
        "fontSize": 10.5,
        "fontBold": false,
        "fontItalic": false,
        "align": "Left",
        "leftIndent": 2,
        "lineSpacing": 1.5,
        "spaceBefore": 0.5,
        "spaceAfter": 0.5
    }'''
    
    # 解析JSON
    config = json.loads(json_string)
    
    # 创建文档并解析
    doc = Document()
    doc = parse_simple_element(doc, config)
    
    # 保存文档
    doc.save("example_json_format.docx")
    print("JSON格式示例完成，文档已保存为 example_json_format.docx")
    print(f"原始JSON字符串：{json_string}")

if __name__ == "__main__":
    print("HTML解析功能使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        print()
        
        example_styled_content()
        print()
        
        example_table_content()
        print()
        
        example_mixed_document()
        print()
        
        example_json_format()
        print()
        
        print("=" * 60)
        print("所有示例运行完成！")
        print("\n使用说明：")
        print("1. HTML内容通过'content'字段传入")
        print("2. 支持的HTML标签：div, h1-h6, p, table, img, figure, ul, ol, li")
        print("3. 支持的样式属性：fontName, fontSize, fontBold, fontItalic, align, leftIndent")
        print("4. 图片会自动下载并插入文档（本地域名：http://127.0.0.1:5579）")
        print("5. 表格支持合并单元格（rowspan, colspan）")
        print("6. 自动跳过script, button等交互标签")
        
    except Exception as e:
        print(f"示例运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()