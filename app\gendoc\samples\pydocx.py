import io
import requests
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches

def convert_html_to_docx(html_content, docx_path):
    """
    将包含复杂表格、段落和图片的HTML内容转换为Word文档。

    :param html_content: HTML内容的字符串
    :param docx_path: 输出的.docx文件路径
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    doc = Document()

    # 遍历所有顶级子元素
    for element in soup.find('body').find_all(recursive=False):
        if element.name == 'p':
            # --- 处理段落 ---
            doc.add_paragraph(element.get_text())

        elif element.name == 'img':
            # --- 处理图片 ---
            img_src = element.get('src')
            if not img_src:
                continue

            try:
                # 假设图片是URL
                response = requests.get(img_src, stream=True)
                response.raise_for_status() # 确保请求成功
                image_stream = io.BytesIO(response.content)
                doc.add_picture(image_stream, width=Inches(5.0)) # 可调整图片宽度
            except requests.exceptions.RequestException as e:
                print(f"无法下载图片 {img_src}: {e}")
                doc.add_paragraph(f"[图片无法加载: {img_src}]")

        elif element.name == 'table':
            # --- 处理表格 (核心部分) ---
            rows_data = element.find_all('tr')
            if not rows_data:
                continue

            # 1. 确定表格的真实维度 (这是一个简化模型，更复杂的需要虚拟网格)
            # 我们先按最大列数创建表格
            max_cols = 0
            for tr in rows_data:
                max_cols = max(max_cols, len(tr.find_all(['td', 'th'])))

            # 创建Word表格
            table = doc.add_table(rows=len(rows_data), cols=max_cols)
            table.style = 'Table Grid' # 使用带边框的样式

            # 2. 填充并合并单元格
            # 创建一个 "虚拟网格" 来跟踪已经被合并的单元格
            grid = [[None for _ in range(max_cols)] for _ in range(len(rows_data))]

            for r_idx, tr in enumerate(rows_data):
                c_idx = 0
                for td in tr.find_all(['td', 'th']):
                    # 找到当前行中第一个可用的单元格
                    while grid[r_idx][c_idx] is not None:
                        c_idx += 1

                    rowspan = int(td.get('rowspan', 1))
                    colspan = int(td.get('colspan', 1))

                    # 填充文本
                    cell = table.cell(r_idx, c_idx)
                    cell.text = td.get_text()

                    # 合并单元格
                    if rowspan > 1 or colspan > 1:
                        top_left_cell = cell
                        bottom_right_cell = table.cell(r_idx + rowspan - 1, c_idx + colspan - 1)
                        top_left_cell.merge(bottom_right_cell)

                    # 在虚拟网格中标记被占用的单元格
                    for i in range(r_idx, r_idx + rowspan):
                        for j in range(c_idx, c_idx + colspan):
                            if i == r_idx and j == c_idx:
                                continue # 跳过左上角单元格本身
                            grid[i][j] = "merged" # 标记为已占用

                    c_idx += colspan

    # 保存文档
    doc.save(docx_path)
    print(f"文档已成功保存到: {docx_path}")


# --- 使用示例 ---
if __name__ == '__main__':
    # 你的HTML内容可以从文件读取或直接放在字符串中
    html_example = """
    <html>
    <body>
        <p>这是一个介绍性段落。</p>
        <p>下面是一个关于公司季度报告的复杂表格。</p>
        <table border="1">
            <tr>
                <th colspan="3">季度销售报告</th>
            </tr>
            <tr>
                <th>月份</th>
                <th>产品A</th>
                <th>产品B</th>
            </tr>
            <tr>
                <td>一月</td>
                <td>500</td>
                <td rowspan="2">1200 (合并)</td>
            </tr>
            <tr>
                <td>二月</td>
                <td>600</td>
            </tr>
            <tr>
                <td colspan="2">总计</td>
                <td>2300</td>
            </tr>
        </table>
        <p>这是一个总结性段落。</p>
        <img src="https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png" alt="Google Logo">
        <p>图片后的最后一段。</p>
    </body>
    </html>
    """

    output_filename = 'output_document.docx'
    convert_html_to_docx(html_example, output_filename)
