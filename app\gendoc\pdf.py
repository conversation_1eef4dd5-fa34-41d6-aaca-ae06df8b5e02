# -*- coding: utf-8 -*-
"""

Pdf文件转换成图片, 然后插入到word文档中.
所有逻辑都来自于wsinst_service.py

"""

import io
import logging
from PIL import Image
from docx.shared import Inches, RGBColor
import pymupdf  # PyMuPDF for PDF processing


logger = logging.getLogger(__name__)

def convert_pdf_to_images(pdf_stream: io.BytesIO) -> list:
    """
    将PDF文件转换为图片列表

    Args:
        pdf_stream: PDF文件的字节流

    Returns:
        list: 包含PIL Image对象的列表，每个对象代表PDF的一页
    """
    images = []
    try:
        # 打开PDF文档
        pdf_document = pymupdf.open(stream=pdf_stream.getvalue(), filetype="pdf")

        # 遍历每一页
        for page_num in range(len(pdf_document)):
            page = pdf_document.load_page(page_num)

            # 设置缩放比例，提高图片质量
            mat = pymupdf.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)

            # 将pixmap转换为PIL Image
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            images.append(img)

        pdf_document.close()

    except Exception as e:
        logger.error(f"PDF转换失败: {e}")
        raise e

    return images

def calculate_image_display_width(image: Image.Image) -> Inches:
    """
    计算图片在文档中的显示宽度

    Args:
        image: PIL Image对象

    Returns:
        Inches: 显示宽度
    """
    width_px, height_px = image.size

    # 计算页面打印区域的70%
    # 假设A4纸的打印区域宽度为8.27英寸，这里取一个安全值7.5英寸
    page_print_width = 7.5  # 英寸
    max_width = page_print_width * 0.7  # 70%的打印区域宽度

    # 计算图片的实际宽度（假设DPI为96）
    dpi = 96  # 标准屏幕DPI
    image_width_inch = width_px / dpi

    # 确定图片显示宽度
    if image_width_inch < max_width:
        # 图片宽度小于页面打印区域的70%，使用原始尺寸
        return Inches(image_width_inch)
    else:
        # 图片宽度大于或等于页面打印区域的70%，按比例缩小到打印区域的70%
        return Inches(max_width)

def process_pdf_file(doc, file_stream: io.BytesIO, file_info: dict):
    """
    处理PDF文件，将每一页转换为图片并插入文档

    Args:
        doc: Word文档对象
        file_stream: PDF文件字节流
        file_info: 文件信息字典
    """
    try:
        # 将PDF转换为图片列表
        images = convert_pdf_to_images(file_stream)

        if not images:
            logger.warning(f"PDF文件 {file_info['name']} 没有可转换的页面")
            return

        # 遍历每一页图片
        for page_num, img in enumerate(images):
            # 计算显示宽度
            display_width = calculate_image_display_width(img)

            # 将PIL Image转换为字节流
            img_stream = io.BytesIO()
            img.save(img_stream, format='PNG')
            img_stream.seek(0)

            # 添加图片到文档
            doc.add_picture(img_stream, width=display_width)

            # 在图片之间添加空行（除了最后一张图片）
            if page_num < len(images) - 1:
                doc.add_paragraph()  # 添加空行

        logger.info(f"成功处理PDF文件 {file_info['name']}，共 {len(images)} 页")

    except Exception as e:
        logger.error(f"处理PDF文件失败 {file_info['name']}: {e}")
        raise e
