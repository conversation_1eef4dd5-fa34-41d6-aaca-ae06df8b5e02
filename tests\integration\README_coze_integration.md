# Coze服务集成测试说明

本目录包含了Coze服务的完整集成测试，用于验证文件上传、智能体调用和状态轮询等核心功能。

## 测试文件说明

### 1. `test_coze_service_integration.py`
**完整版集成测试**
- 包含完整的错误处理和详细日志
- 支持长时间运行（默认超时20分钟）
- 适用于生产环境的完整功能验证

### 2. `test_coze_service_demo.py`
**演示版集成测试**
- 简化的输出格式，便于理解
- 较短的超时时间（1分钟），适合快速验证
- 包含详细的测试步骤说明
- 推荐用于功能演示和快速测试

## 测试流程

### 步骤1: 文件上传
- 上传 `tests/test_data/简介.docx`
- 上传 `tests/test_data/人数和水量.jpg`
- 收集文件ID并构建文件列表

### 步骤2: 智能体调用
- 使用配置的token、bot_id和user_id
- 传入上传的文件列表
- 获取对话ID和聊天ID

### 步骤3: 状态轮询
- 定期查询对话状态
- 使用回调函数处理完成和错误事件
- 支持超时处理

## 配置参数

```python
# 测试参数（在测试文件中配置）
TOKEN = "pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo"
BOT_ID = "7541317373962747945"
USER_ID = "lcmp123456789"

# 测试文件
TEST_FILES = [
    "tests/test_data/简介.docx",
    "tests/test_data/人数和水量.jpg"
]
```

## 运行测试

### 快速演示测试
```bash
# 在项目根目录下运行
python tests/integration/test_coze_service_demo.py
```

### 完整集成测试
```bash
# 在项目根目录下运行
python tests/integration/test_coze_service_integration.py
```

## 预期输出

### 成功场景
```
🚀 Coze服务集成测试 - 演示版本
============================================================

=== 测试步骤1: 文件上传 ===
📁 上传文件 1/2: 简介.docx
✅ 上传成功，文件ID: {'type': 'file', 'file_id': '7544956552047411263'}
📁 上传文件 2/2: 人数和水量.jpg
✅ 上传成功，文件ID: {'type': 'image', 'file_id': '7544956687279783936'}
📊 文件上传汇总: 成功上传 2 个文件

=== 测试步骤2: 智能体调用 ===
✅ 智能体调用成功
📋 对话ID: 7544956919917576244
📋 聊天ID: 7544956919917641780
✅ 返回结果验证通过

=== 测试步骤3: 状态轮询 ===
📋 监控对话: 7544956919917576244
📋 监控聊天: 7544956919917641780
对话状态: failed, 5秒后重新查询...
...
✅ 状态轮询功能测试完成

============================================================
🎉 集成测试演示完成！
📊 测试结果汇总:
   ✅ 文件上传功能: 正常
   ✅ 智能体调用功能: 正常
   ✅ 状态轮询功能: 正常
   ✅ 回调函数机制: 正常
============================================================
```

## 回调函数说明

### 成功回调 (`success_callback`)
```python
def success_callback():
    """对话完成成功回调函数 - 简单打印日志"""
    print("🎉 [回调] 对话已成功完成！")
    print("📝 [回调] 智能体已处理完所有上传的文件")
```

### 错误回调 (`error_callback`)
```python
def error_callback(exception: Exception):
    """对话出错回调函数 - 简单打印日志"""
    print(f"❌ [回调] 对话过程中发生错误: {type(exception).__name__}")
    print(f"📄 [回调] 错误详情: {str(exception)}")
```

## 测试验证点

### 文件上传验证
- ✅ 文件存在性检查
- ✅ 上传成功返回文件ID
- ✅ 文件类型正确识别（docx为file，jpg为image）
- ✅ 文件列表构建正确

### 智能体调用验证
- ✅ API调用成功
- ✅ 返回结果为字典类型
- ✅ 包含必需字段（conversation_id, id）
- ✅ 字段值非空

### 状态轮询验证
- ✅ 轮询机制正常工作
- ✅ 间隔时间控制正确
- ✅ 超时处理机制有效
- ✅ 回调函数正确触发
- ✅ 异常处理机制完善

## 注意事项

1. **网络依赖**: 测试需要访问Coze API，确保网络连接正常
2. **Token有效性**: 确保使用的token有效且有足够权限
3. **文件准备**: 确保测试文件存在于指定路径
4. **超时设置**: 根据实际网络情况调整超时参数
5. **错误处理**: 网络问题不会影响功能测试的完整性验证

## 故障排除

### 常见问题

1. **文件不存在错误**
   - 检查 `tests/test_data/` 目录下是否存在测试文件
   - 确保文件名正确（简介.docx, 人数和水量.jpg）

2. **网络连接错误**
   - 检查网络连接
   - 确认可以访问 api.coze.cn
   - 检查防火墙设置

3. **认证错误**
   - 验证token是否有效
   - 检查bot_id是否正确
   - 确认用户权限

4. **对话状态为failed**
   - 这可能是正常的智能体处理状态
   - 重点关注轮询机制和回调函数是否正常工作
   - 检查智能体配置是否正确

## 扩展开发

如需添加新的测试场景，可以：

1. 在现有测试文件中添加新的测试函数
2. 创建新的测试文件并遵循相同的命名规范
3. 更新回调函数以支持更复杂的处理逻辑
4. 添加更多的验证点和断言

## 版本历史

- v1.0.0: 初始版本，包含基本的集成测试功能
- 支持文件上传、智能体调用、状态轮询的完整流程测试
- 包含详细的日志输出和错误处理机制