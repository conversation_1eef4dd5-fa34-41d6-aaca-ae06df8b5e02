# -*- coding: utf-8 -*-
"""
AI Hub服务器主应用

基于FastAPI框架的HTTP服务器，提供音频转写、聊天和文档生成等API服务。
"""

import os
import sys
import time
import importlib.util
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from starlette.exceptions import HTTPException as StarletteHTTPException

# 导入项目内部模块
from .utils.http import create_error_response
from .core.logger import get_logger, log_request
from .core.config import get_config_manager
from .services.chat_service import init_chat_client
from .core.exceptions import (
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from .routers import tingwu, chat, gendocx, coze, wsinst, wsinst2

# 获取环境配置
config_manager = get_config_manager()  # 自动从环境变量或默认值获取环境
environment = config_manager.environment

# 只保留日志记录器获取
logger = get_logger()

# 记录配置信息
config_info = config_manager.get_config_info()
logger.info(f"应用启动，环境: {environment}, 配置来源: {config_info['config_sources']}")
if config_info.get('config_file_path'):
    logger.info(f"配置文件: {config_info['config_file_path']}")


def check_python_version():
    """
    检查Python版本
    """
    if sys.version_info < (3, 10):
        logger.error("错误: 需要Python 3.10或更高版本")
        logger.error(f"当前版本: {sys.version}")
        return False
    else:
        logger.info(f"Python版本检查通过: {sys.version.split()[0]}")
        return True


def check_dependencies():
    """
    检查依赖包是否安装
    """
    required_packages = [
        'fastapi',
        'uvicorn',
        'alibabacloud_tea_openapi',
        'alibabacloud_credentials'
    ]

    missing_packages = []

    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行以下命令安装依赖:")
        logger.error("pip install -r requirements.txt")
        return False
    else:
        logger.info("依赖包检查通过")
        return True


def check_credentials():
    """
    检查阿里云凭证配置
    """
    # 通过配置管理器获取凭证
    credentials = config_manager.get_aliyun_credentials()
    access_key_id = credentials.get('access_key_id')
    access_key_secret = credentials.get('access_key_secret')

    if access_key_id and access_key_secret:
        logger.info("发现阿里云凭证配置")
        return True

    logger.warning("警告: 未发现阿里云凭证配置")
    logger.warning("请到配置文件中添加以下凭证信息:")
    logger.warning("  ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id")
    logger.warning("  ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret")
    return False


# 创建FastAPI应用
app = FastAPI(
    title="AI Hub服务器",
    description="基于FastAPI的HTTP服务器，提供音频转写、聊天和文档生成等API服务",
    version="1.0.0"
)

# 初始化大模型文本交互客户端
init_chat_client(environment=environment)

# 注册路由
app.include_router(tingwu.router)
app.include_router(chat.router)
app.include_router(gendocx.router)
app.include_router(coze.router)
app.include_router(wsinst.router)
app.include_router(wsinst2.router)

# 配置静态文件服务
# 静态文件目录路径，通过配置管理器读取
static_dir = config_manager.get_static_dir()
static_path = Path(__file__).parent.parent / static_dir

# 确保静态文件目录存在
if not static_path.exists():
    static_path.mkdir(parents=True, exist_ok=True)
    logger.info(f"创建静态文件目录: {static_path}")

# 挂载静态文件路由
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
logger.info(f"静态文件服务已启用，路径: {static_path}, 访问路径: /static")


@app.get('/health')
async def health_check():
    """
    健康检查端点

    Returns:
        Dict: 服务状态信息
    """
    start_time = time.time()

    try:
        # 记录健康检查请求
        log_request('GET', '/health')

        response_data = {
            "status": "healthy",
            "service": "aihub server",
            "version": "1.0.0",
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        response_time = time.time() - start_time
        log_request('GET', '/health', response_time=response_time)

        return response_data

    except Exception as e:
        response_time = time.time() - start_time
        error_msg = f"健康检查失败: {str(e)}"
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")
        log_request('GET', '/health', response_time=response_time)
        return create_error_response(error_msg, 500)





# 注册异常处理器
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)






def create_app():
    """
    创建FastAPI应用实例

    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    return app
