import latex2mathml.converter
from lxml import etree
from docx import Document
from docx.oxml import parse_xml

def latex_to_mathml(latex):
    """Convert LaTeX formula to MathML."""
    return latex2mathml.converter.convert(latex)

def mathml_to_omml(mathml, xslt_file='MML2OMML.XSL'):
    """Convert MathML to OMML using XSLT transformation."""
    xslt = etree.parse(xslt_file)
    transform = etree.XSLT(xslt)
    mathml_tree = etree.fromstring(mathml)
    omml_tree = transform(mathml_tree)
    return etree.tostring(omml_tree, encoding='unicode')

def add_formula_to_doc(doc, omml):
    """Add OMML formula to the DOCX document."""
    p = doc.add_paragraph()
    p._element.append(parse_xml(omml))

def main():
    # Example LaTeX formula
    latex_formula = r'\frac{1}{2} + \sqrt{3}'
    
    # Convert LaTeX to MathML
    mathml = latex_to_mathml(latex_formula)
    
    # Convert MathML to OMML
    omml = mathml_to_omml(mathml)
    
    # Create a new DOCX document
    doc = Document()
    
    # Add the formula to the document
    add_formula_to_doc(doc, omml)
    
    # Save the document
    doc.save('formula.docx')

if __name__ == '__main__':
    main()