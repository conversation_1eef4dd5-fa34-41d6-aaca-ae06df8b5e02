#!/bin/bash

# AIHub 服务启动脚本
# 功能：后台运行FastAPI + Uvicorn服务，支持自动重启

# 设置脚本目录为工作目录
cd "$(dirname "$0")"

# 日志文件路径
LOG_FILE="./logs/aihub.log"
ERROR_LOG_FILE="./logs/aihub_error.log"

# 创建日志目录（如果不存在）
mkdir -p logs

# 服务启动函数
start_service() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动 AIHub 服务 (Gunicorn + Uvicorn Worker)..." >> "$LOG_FILE"
    # 只将stdout重定向到LOG_FILE，gunicorn自己管理errorlog输出
    gunicorn app.app:create_app --config config/gunicorn.conf.py >> "$LOG_FILE" 2>&1
    exit_code=$?
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务退出，退出码: $exit_code" >> "$LOG_FILE"
    return $exit_code
}

# 检查是否已有服务在运行
check_running() {
    pgrep -f "gunicorn.*app.app:create_app" > /dev/null
    return $?
}

# 主循环 - 自动重启逻辑
main_loop() {
    while true; do
        start_service
        exit_code=$?

        # 如果退出码为0，说明是正常退出，不重启
        if [ $exit_code -eq 0 ]; then
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务正常退出，停止重启" >> "$LOG_FILE"
            break
        fi

        # 异常退出，等待5秒后重启
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务异常退出，5秒后重启..." >> "$LOG_FILE"
        sleep 5
    done
}

# 启动脚本主逻辑
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动 AIHub 守护进程" >> "$LOG_FILE"

# 检查是否已有服务运行
if check_running; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 检测到服务已在运行，请先停止现有服务" >> "$LOG_FILE"
    echo "服务已在运行，请使用 './stop.sh' 停止现有服务后再启动"
    exit 1
fi

# 使用 nohup 在后台运行主循环
nohup bash -c 'source "$(dirname "$0")/start.sh" && main_loop' > /dev/null 2>&1 &

# 获取后台进程PID并保存
echo $! > ./logs/aihub.pid

echo "AIHub 服务已在后台启动"
echo "PID: $(cat ./logs/aihub.pid)"
echo "主日志: $LOG_FILE"
echo "Gunicorn错误日志: ./logs/aihub_gunicorn_error.log"
echo "使用 'tail -f $LOG_FILE' 查看实时日志"
echo "使用 './stop.sh' 停止服务"
