#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件测试脚本
用于验证三个配置文件的读取是否正常工作
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from app.core.config import get_config_manager
from app.services.tingwu_service import TingwuClient
from app.services.chat_service import ChatClient
from app.core.logger import get_logger

def test_config_files():
    """测试配置文件读取"""
    logger = get_logger()

    print("=" * 60)
    print("配置文件测试")
    print("=" * 60)

    # 1. 测试config.ini读取
    print("\n1. 测试 config.ini 读取:")
    try:
        config_manager = get_config_manager('default')
        app_key = config_manager.get_app_key()
        host = config_manager.get('host')
        port = config_manager.get('port')

        print(f"   ✓ app_key: {app_key[:8] + '***' if app_key and len(app_key) > 8 else '未配置'}")
        print(f"   ✓ host: {host}")
        print(f"   ✓ port: {port}")

        config_info = config_manager.get_config_info()
        print(f"   ✓ 配置文件路径: {config_info.get('config_file_path', '未找到')}")
        print(f"   ✓ 配置来源: {config_info['config_sources']}")

    except Exception as e:
        print(f"   ✗ config.ini 读取失败: {e}")

    # 2. 测试credentials.ini读取
    print("\n2. 测试 ~/.alibabacloud/credentials.ini 读取:")
    try:
        credentials_path = Path.home() / '.alibabacloud' / 'credentials.ini'
        print(f"   配置文件路径: {credentials_path}")
        print(f"   文件存在: {credentials_path.exists()}")

        if credentials_path.exists():
            credentials = config_manager.get_aliyun_credentials()
            print(f"   ✓ access_key_id: {credentials['access_key_id'][:8] + '***' if credentials['access_key_id'] else '未配置'}")
            print(f"   ✓ region_id: {credentials['region_id']}")
        else:
            print(f"   ⚠ 文件不存在，请创建: {credentials_path}")
            print(f"   ⚠ 参考格式见: ./config/credentials.ini")

    except Exception as e:
        print(f"   ✗ credentials.ini 读取失败: {e}")

    # 3. 测试chat.ini读取
    print("\n3. 测试 ~/.alibabacloud/chat.ini 读取:")
    try:
        chat_path = Path.home() / '.alibabacloud' / 'chat.ini'
        print(f"   配置文件路径: {chat_path}")
        print(f"   文件存在: {chat_path.exists()}")

        if chat_path.exists():
            chat_client = ChatClient('default')
            print(f"   ✓ ChatClient 初始化成功")
        else:
            print(f"   ⚠ 文件不存在，请创建: {chat_path}")
            print(f"   ⚠ 参考格式见: ./config/chat.ini")

    except Exception as e:
        print(f"   ✗ chat.ini 读取失败: {e}")

    # 4. 测试TingwuClient初始化
    print("\n4. 测试 TingwuClient 初始化:")
    try:
        tingwu_client = TingwuClient('default')
        print(f"   ✓ TingwuClient 初始化成功")
    except Exception as e:
        print(f"   ✗ TingwuClient 初始化失败: {e}")

    print("\n" + "=" * 60)
    print("配置指导:")
    print("=" * 60)
    print("1. 复制 ./config/credentials.ini 到 ~/.alibabacloud/credentials.ini")
    print("2. 复制 ./config/chat.ini 到 ~/.alibabacloud/chat.ini")
    print("3. 编辑 ~/.alibabacloud/credentials.ini，填入真实的阿里云凭证和app_key")
    print("4. 编辑 ~/.alibabacloud/chat.ini，填入真实的聊天API密钥")
    print("\n命令示例:")
    print("mkdir -p ~/.alibabacloud")
    print("cp ./config/credentials.ini ~/.alibabacloud/")
    print("cp ./config/chat.ini ~/.alibabacloud/")
    print("# 然后编辑这些文件，填入真实的密钥")

if __name__ == '__main__':
    test_config_files()