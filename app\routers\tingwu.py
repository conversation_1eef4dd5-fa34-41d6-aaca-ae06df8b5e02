# -*- coding: utf-8 -*-
"""
千义听悟API路由

处理音频转写相关的API请求。
"""

import json
import time
from fastapi import APIRouter, Request
from pydantic import ValidationError

# 导入项目内部模块
from ..services.tingwu_service import TingwuClient
from ..utils.http import create_error_response
from ..core.logger import get_logger, log_request, log_api_call
from ..core.config import get_config_manager
from ..models.requests import TingwuRequest, validate_tingwu_request
from ..models.responses import TingwuResponse

# 创建路由器
router = APIRouter(
    prefix="/api/v1/voice/tingwu",
    tags=["千义听悟"],
    responses={404: {"description": "Not found"}}
)

# 获取配置和日志
config_manager = get_config_manager()
environment = config_manager.environment
logger = get_logger()

# 初始化千义听悟客户端
tingwu_client = TingwuClient(environment=environment)


@router.post('/new')
async def create_transcription_task(request: Request):
    """
    创建音频转写任务端点

    接收POST请求，创建千义听悟音频转写任务。

    请求体格式:
    {
        "TaskKey": "task_tingwu_123",
        "fileUrl": "http://example.com/audio.mp3",
        "sourceLanguage": "cn",  // 可选，默认为"cn"
        "sampleRate": 16000,  // 可选，默认为16000
        "speakerCount": 0,  // 可选，默认为0（自动检测）
        "outputLevel": 1,  // 可选，默认为1
        "diarizationEnabled": true,  // 可选，默认为true
        "progressiveCallbacksEnabled": true,  // 可选，默认为true
        "translationEnabled": false,  // 可选，默认为false
        "autoChaptersEnabled": false,  // 可选，默认为false
        "meetingAssistanceEnabled": false,  // 可选，默认为false
        "summarizationEnabled": false  // 可选，默认为false
    }

    Returns:
        JSON响应: 任务创建结果
    """
    start_time = time.time()

    try:
        # 记录请求日志
        log_request('POST', '/api/v1/voice/tingwu/new')

        # 获取请求数据
        try:
            data = await request.json()
        except Exception as e:
            logger.error(f"解析JSON请求体失败: {str(e)}")
            return create_error_response("请求体必须是有效的JSON格式", 400)

        if not data:
            logger.error("请求体为空")
            return create_error_response("请求体不能为空", 400)

        # 记录请求数据（隐藏敏感信息）
        safe_data = {k: v for k, v in data.items() if k != 'fileUrl'}
        safe_data['fileUrl'] = '***' if 'fileUrl' in data else None
        logger.info(f"收到创建转写任务请求: {json.dumps(safe_data, ensure_ascii=False)}")

        # 使用Pydantic验证请求参数
        try:
            validated_request = validate_tingwu_request(data)
            logger.info("请求参数验证通过")
        except ValidationError as e:
            # 格式化Pydantic验证错误信息
            error_details = []
            for error in e.errors():
                field = '.'.join(str(loc) for loc in error['loc'])
                message = error['msg']
                error_details.append(f"{field}: {message}")
            validation_error = "; ".join(error_details)
            logger.error(f"请求参数验证失败: {validation_error}")
            return create_error_response(f"请求参数验证失败: {validation_error}", 400)
        except Exception as e:
            validation_error = str(e)
            logger.error(f"请求参数验证失败: {validation_error}")
            return create_error_response(f"请求参数验证失败: {validation_error}", 400)

        # 提取参数
        task_key = data['TaskKey']
        file_url = data['fileUrl']

        # 可选参数，使用默认值
        source_language = data.get('sourceLanguage', 'cn')
        sample_rate = data.get('sampleRate', 16000)
        speaker_count = data.get('speakerCount', 0)
        output_level = data.get('outputLevel', 1)
        diarization_enabled = data.get('diarizationEnabled', True)
        progressive_callbacks_enabled = data.get('progressiveCallbacksEnabled', True)
        translation_enabled = data.get('translationEnabled', False)
        auto_chapters_enabled = data.get('autoChaptersEnabled', False)
        meeting_assistance_enabled = data.get('meetingAssistanceEnabled', False)
        summarization_enabled = data.get('summarizationEnabled', False)

        # 获取自定义prompt参数
        custom_prompt = data.get('Prompts', None)

        logger.info(f"开始创建转写任务，TaskKey: {task_key}")

        # 记录API调用
        api_params = {
            'task_key': task_key,
            'source_language': source_language,
            'sample_rate': sample_rate,
            'speaker_count': speaker_count,
            'output_level': output_level
        }

        # 调用千义听悟API创建任务
        result = tingwu_client.create_transcription_task(
            task_key=task_key,
            file_url=file_url,
            source_language=source_language,
            sample_rate=sample_rate,
            speaker_count=speaker_count,
            output_level=output_level,
            diarization_enabled=diarization_enabled,
            progressive_callbacks_enabled=progressive_callbacks_enabled,
            translation_enabled=translation_enabled,
            auto_chapters_enabled=auto_chapters_enabled,
            meeting_assistance_enabled=meeting_assistance_enabled,
            summarization_enabled=summarization_enabled,
            custom_prompt_contents=custom_prompt
        )

        # 记录成功的API调用
        response_time = time.time() - start_time
        log_api_call('create_transcription_task', api_params, {'task_key': task_key, 'status': 'success'})
        log_request('POST', '/api/v1/voice/tingwu/new', response_time=response_time)

        logger.info(f"转写任务创建成功，TaskKey: {task_key}, 耗时: {response_time:.3f}s")

        # 返回API响应结果
        return result

    except Exception as e:
        error_msg = f"创建转写任务失败: {str(e)}"
        response_time = time.time() - start_time

        # 记录失败的API调用
        if 'task_key' in locals():
            log_api_call('create_transcription_task', api_params if 'api_params' in locals() else {}, error=error_msg)

        log_request('POST', '/api/v1/voice/tingwu/new', response_time=response_time)
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")
        return create_error_response(error_msg, 500)