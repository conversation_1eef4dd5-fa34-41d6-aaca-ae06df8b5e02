#!/bin/bash

# AIHub 服务停止脚本
# 功能：停止Gunicorn + Uvicorn Worker服务

# 设置脚本目录为工作目录
cd "$(dirname "$0")"

# PID文件路径
PID_FILE="./logs/aihub.pid"
LOG_FILE="./logs/aihub.log"

echo "正在停止 AIHub 服务..."


# 停止所有相关进程
pkill -f "start.sh"
pkill -f "gunicorn.*lcmp_aihub_app"

# 等待进程优雅退出
sleep 3

# 优雅停止gunicorn主进程
if pgrep -f "gunicorn.*lcmp_aihub_app" > /dev/null; then
    echo "发送SIGTERM信号进, 服务即将停止..."
    pkill -TERM -f "gunicorn.*lcmp_aihub_app"

    # 等待5秒让进程优雅退出
    sleep 5

    # 如果还有进程在运行，强制停止
    if pgrep -f "gunicorn.*lcmp_aihub_app" > /dev/null; then
        echo "强制停止剩余进程..."
        pkill -KILL -f "gunicorn.*lcmp_aihub_app"
    fi
fi

# 如果PID文件存在，尝试杀死主进程
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        kill "$PID"
        echo "已发送停止信号给进程 $PID"
    fi
    rm -f "$PID_FILE"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务已停止" >> "$LOG_FILE"
echo "AIHub 服务已停止"
