from __future__ import annotations

import io
import requests
from bs4 import BeautifulSoup
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.section import WD_SECTION_START, WD_ORIENT
from html4docx import HtmlToDocx
from app.core.logger import get_logger
from .parse_html import parse_html_dom
from .pdf import process_pdf_file


from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from docx.document import Document as DocumentObject

# 初始化日志记录器
logger = get_logger(__name__)



LOCAL_ASSETS_DOMAIN = "http://127.0.0.1:5577" # 本地资源服务器地址, 已通过nginx代理, 用于解析HTML中的相对路径


'''
- 初号：42磅
- 小初：36磅
- 一号：26磅
- 小一：24磅
- 二号：22磅
- 小二：18磅
- 三号：16磅
- 小三：15磅
- 四号：14磅
- 小四：12磅
- 五号：10.5磅
- 小五：9磅
- 六号：7.5磅
- 小六：6.5磅
- 七号：5.5磅
- 八号：5磅
'''
chinese_fontsize = {
    '初号': Pt(42),
    '小初': Pt(36),
    '一号': Pt(26),
    '小一': Pt(24),
    '二号': Pt(22),
    '小二': Pt(18),
    '三号': Pt(16),
    '小三': Pt(15),
    '四号': Pt(14),
    '小四': Pt(12),
    '五号': Pt(10.5),
    '小五': Pt(9),
    '六号': Pt(7.5),
    '小六': Pt(6.5),
    '七号': Pt(5.5),
    '八号': Pt(5),
}

# 段落对齐方式
align_map = {
    'LEFT': WD_ALIGN_PARAGRAPH.LEFT,
    'CENTER': WD_ALIGN_PARAGRAPH.CENTER,
    'RIGHT': WD_ALIGN_PARAGRAPH.RIGHT,
    'JUSTIFY': WD_ALIGN_PARAGRAPH.JUSTIFY,
}

def is_real_number(value):
    return isinstance(value, (int, float)) and not isinstance(value, bool)

def normalize_font_size(font_size):
    """
    把字体大小转换为word文档中的字体大小.
    """
    if is_real_number(font_size):
        return Pt(font_size)
    elif font_size in chinese_fontsize.keys():
        return chinese_fontsize.get(font_size, 10.5)
    else:
        return Pt(10.5)

def parse_font_color(color_str):
    """
    解析字体颜色字符串为RGBColor对象
    支持格式: "000000", "#000000", "black" 等
    """
    if not color_str:
        return None

    # 移除 # 前缀
    if color_str.startswith('#'):
        color_str = color_str[1:]

    # 预定义颜色映射
    color_map = {
        'black': '000000',
        'white': 'FFFFFF',
        'red': 'FF0000',
        'green': '00FF00',
        'blue': '0000FF',
        'yellow': 'FFFF00',
        'cyan': '00FFFF',
        'magenta': 'FF00FF'
    }

    # 如果是预定义颜色名称
    if color_str.lower() in color_map:
        color_str = color_map[color_str.lower()]

    # 验证是否为有效的6位十六进制颜色
    if len(color_str) == 6 and all(c in '0123456789ABCDEFabcdef' for c in color_str):
        try:
            # 使用 from_string 方法创建 RGBColor 对象
            return RGBColor.from_string(color_str)
        except ValueError:
            pass

    # 默认返回黑色
    return RGBColor.from_string('000000')

def add_toc(doc: DocumentObject) -> DocumentObject:
    """向文本对象添加目录(table of contents)."""
    # Add a paragraph for the TOC
    paragraph = doc.add_paragraph()
    run = paragraph.add_run()

    # Create the TOC field code: {TOC \o "1-3" \h \z \u}
    fldChar1 = OxmlElement('w:fldChar')
    fldChar1.set(qn('w:fldCharType'), 'begin')

    instrText = OxmlElement('w:instrText')
    instrText.set(qn('xml:space'), 'preserve')
    instrText.text = 'TOC \\o "1-3" \\h \\z \\u'

    fldChar2 = OxmlElement('w:fldChar')
    fldChar2.set(qn('w:fldCharType'), 'separate')

    fldChar3 = OxmlElement('w:fldChar')
    fldChar3.set(qn('w:fldCharType'), 'end')

    # Append the field elements to the run
    run._r.append(fldChar1)
    run._r.append(instrText)
    run._r.append(fldChar2)
    run._r.append(fldChar3)

    # Add a note to update the TOC
    doc.add_paragraph('[在上一空行处点击右键选择"更新域"生成目录, 生成目录后请将本行删除!]')
    return doc

def init_doc_orientation(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    Sets the orientation of the document's first section before adding content.
    :param doc: The Document object.
    :param orientation: 'portrait' or 'landscape' (default: 'landscape').
    """
    orientation = info.get('orient', 'portrait').lower()
    section = doc.sections[0]
    if orientation == 'landscape':
        section.orientation = WD_ORIENT.LANDSCAPE
        # Swap dimensions to match the new orientation
        new_width = section.page_height
        new_height = section.page_width
        section.page_width = new_width
        section.page_height = new_height
    elif orientation == 'portrait':
        section.orientation = WD_ORIENT.PORTRAIT
        # For portrait, dimensions might already be set, but swap if needed
        new_width = section.page_height
        new_height = section.page_width
        section.page_width = new_height  # Assuming it was landscape before
        section.page_height = new_width
    else:
        raise ValueError("Orientation should be 'portrait' or 'landscape'.")
    return doc

def set_global_orientation(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    设置全局页面方向.
    """
    orientation = info.get('orient', 'portrait').lower()
    if orientation not in ['portrait', 'landscape']:
        raise ValueError("方向参数必须是 'portrait' 或 'landscape'")
    for section in doc.sections:
        current_width, current_height = section.page_width, section.page_height
        if orientation == 'landscape':
            section.orientation = WD_ORIENT.LANDSCAPE
            section.page_width = current_height
            section.page_height = current_width
        else:
            section.orientation = WD_ORIENT.PORTRAIT
            section.page_width = min(current_width, current_height)
            section.page_height = max(current_width, current_height)
    return doc

def set_section_orientation(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    设置当前节页面方向.
    """
    orientation = info.get('orient', 'portrait').lower()
    if orientation not in ['portrait', 'landscape']:
        raise ValueError("方向参数必须是 'portrait' 或 'landscape'")

    # 添加一个分节符，新节从下一页开始
    # 这会返回新创建的节
    new_section = doc.add_section(WD_SECTION_START.NEW_PAGE)

    # 获取新节的尺寸 (它会继承前一个节的设置)
    current_width, current_height = new_section.page_width, new_section.page_height

    # 判断并设置新节的页面方向和尺寸
    if orientation == 'landscape':
        new_section.orientation = WD_ORIENT.LANDSCAPE
        new_section.page_width = current_height
        new_section.page_height = current_width
    else:
        new_section.orientation = WD_ORIENT.PORTRAIT
        new_section.page_width = min(current_width, current_height)
        new_section.page_height = max(current_width, current_height)
    return doc

def add_custom_enter(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义段落.
    JSON: "{"Type": "Enter", "useTemp": false, "fontSize": "小一"}"
    """
    p = doc.add_paragraph()
    #run = p.add_run('')
    if info.get('useTemp', False):
        pass
    else:
        # 设置字体大小
        font_size = normalize_font_size(info.get('fontSize', 10.5))

        p_format = p.paragraph_format

        # 将行距规则设置为“固定值”，并指定其高度, 这是强制锁定高度的关键，确保视觉效果正确无误。
        target_size = Pt(1.2 * font_size.pt) # 使用1.2倍字体大小作为间距，模拟MS Word中空段落行高与字号挂钩的效果
        p_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
        p_format.line_spacing = target_size

        # 在该段落中添加一个带零宽空格的Run, 使用零宽空格 '\u200B' 作为“挂载”字体格式的实体。
        run = p.add_run('\u200B')
        run.font.size = target_size

    return doc

def add_custom_new_page(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加新页面.
    JSON: "{\"Type\":\"NewPage\"}"
    """
    doc.add_page_break()
    return doc


def add_custom_title(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加标题.
    JSON: "{\"type\":\"Title\",\"useTemp\":true,\"content\":\"标题\",\"fontName\":\"SimSun\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}"
    """
    text = info.get('content', '标题')
    level = info.get('level', 0)
    title = doc.add_heading('', level = level)
    run = title.add_run(text)
    if info.get('useTemp', False):
        pass
    else:
        font_name = info.get('fontName', 'SimSun')
        font_size = normalize_font_size(info.get('fontSize'))
        font_bold = info.get('fontBold', False)
        font_italic = info.get('fontItalic', False)
        font_color = info.get('fontColor', '000000')  # 默认黑色
        align = align_map.get(info.get('align', 'center').upper(), WD_ALIGN_PARAGRAPH.CENTER)
        run.font.name = font_name
        run.font.size = font_size
        run.font.bold = font_bold
        run.font.italic = font_italic
        # 设置字体颜色
        if font_color:
            color_obj = parse_font_color(font_color)
            if color_obj:
                run.font.color.rgb = color_obj
        # 设置东亚字体以确保中文字符正确显示
        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        title.alignment = align
    return doc

def add_custom_subtitle(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加副标题.
    JSON: "{\"type\":\"SubTitle\",\"useTemp\":true,\"content\":\"副标题\",\"fontName\":\"SimHei\",\"fontSize\":20,\"fontBold\":true,\"fontItalic\":false}"
    """
    text = info.get('content', '副标题')

    if info.get('useTemp', False):
        # 使用模板中定义的Subtitle样式
        subtitle = doc.add_paragraph(style='Subtitle')
        run = subtitle.add_run(text)
    else:
        # 创建普通段落，使用自定义样式
        subtitle = doc.add_paragraph('')
        run = subtitle.add_run(text)

        font_name = info.get('fontName', 'SimHei')
        font_size = normalize_font_size(info.get('fontSize', 20))  # 默认副标题字号
        font_bold = info.get('fontBold', True)  # 默认加粗
        font_italic = info.get('fontItalic', False)
        font_color = info.get('fontColor', '000000')  # 默认黑色
        align = align_map.get(info.get('align', 'center').upper(), WD_ALIGN_PARAGRAPH.CENTER)
        run.font.name = font_name
        run.font.size = font_size
        run.font.bold = font_bold
        run.font.italic = font_italic
        # 设置字体颜色
        if font_color:
            color_obj = parse_font_color(font_color)
            if color_obj:
                run.font.color.rgb = color_obj
        # 设置东亚字体以确保中文字符正确显示
        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        subtitle.alignment = align

    return doc

def add_custom_heading(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加标题.
    JSON: "{\"type\":\"Heading\",\"useTemp\":true,\"content\":\"第一章\",\"level\":1,\"fontName\":\"SimHei\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}"
    """
    text = info.get('content', '标题')
    level = info.get('level', info.get('Level', 1))  # 支持 level 和 Level 两种参数名
    #doc.add_heading(content, level) # 无法添加自定义样式
    paragraph = doc.add_paragraph(style=f"Heading {level}")
    run = paragraph.add_run(text)
    if info.get('useTemp', False):
        pass
    else:
        font_name = info.get('fontName', 'SimHei')
        font_size = normalize_font_size(info.get('fontSize'))
        font_bold = info.get('fontBold', False)
        font_italic = info.get('fontItalic', False)
        font_color = info.get('fontColor', '000000')  # 默认黑色
        align = align_map.get(info.get('align', 'left').upper(), WD_ALIGN_PARAGRAPH.LEFT)
        run.font.name = font_name
        run.font.size = font_size
        run.font.bold = font_bold
        run.font.italic = font_italic
        # 设置字体颜色
        if font_color:
            color_obj = parse_font_color(font_color)
            if color_obj:
                run.font.color.rgb = color_obj
        # 设置东亚字体以确保中文字符正确显示
        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        paragraph.alignment = align
    return doc

def add_custom_figure(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义图像.
    JSON:"{\"type\":\"Figure\",\"useTemp\":true,\"content\":\"http://127.0.0.1/test.png\",\"caption\":\"图片标题\"}"
    """
    uri = info.get('content', '')
    if not uri:
        logger.warning(f"图片地址为空, 跳过添加图片")
        return doc
    src = uri if uri.startswith('http') else LOCAL_ASSETS_DOMAIN + uri
    caption = info.get('caption', '')
    try:
        response = requests.get(src, stream=True)
        response.raise_for_status() # 确保请求成功
        image_stream = io.BytesIO(response.content)
        doc.add_picture(image_stream, width=Inches(5.0)) # 可调整图片宽度
    except requests.exceptions.RequestException as e:
        logger.info(f"无法下载图片 {src}: {e}")
        doc.add_paragraph(f"[图片无法加载: {src}]")
    if caption:
        doc.add_paragraph(caption)
    return doc

def add_custom_pdf_img(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义图像.
    JSON:"{\"type\":\"Pdfimg\",\"useTemp\":true,\"content\":\"http://127.0.0.1/test.pdf\",\"caption\":\"pdf标题\",\"name\":\"pdf文件名\"}"
    """
    uri = info.get('content', '')
    if not uri:
        logger.warning(f"pdf文件地址为空, 跳过添加pdf文件")
        return doc
    src = uri if uri.startswith('http') else LOCAL_ASSETS_DOMAIN + uri
    caption = info.get('caption', '')
    try:
        response = requests.get(src, stream=True)
        response.raise_for_status() # 确保请求成功
        file_stream = io.BytesIO(response.content)

        # 标题在上方
        if caption:
            doc.add_paragraph(caption)
        process_pdf_file(doc, file_stream, info)

    except requests.exceptions.RequestException as e:
        logger.info(f"无法下载pdf文件 {src}: {e}")
        doc.add_paragraph(f"[pdf文件无法加载: {src}]")

    return doc

def add_custom_table(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义表格.
    JSON: "{\"type\":\"Table\",\"useTemp\":true,\"content\":\"<table class=\\\"''table-to-export\\\"'' style=\\\"''border-collapse: collapse; border: 2px solid rgb(140 140 140); font-family: sans-serif; font-size: 0.8rem; letter-spacing: 1px;\\\"'' data-tbl-name=\\\"''4.1 企业水平衡测试统计表\\\"''><thead><tr><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>用水分类</th><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>序号</th><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>用水单元名称</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''2\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>取水量(m³/d)</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''6\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>重复利用水量(m³/d)</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他水量(m³/d)</th></tr><tr><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>常规水资源量</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>非常规水资源量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>直接冷却循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>间接循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>蒸汽冷凝回用量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>回用水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他串联水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>排水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>漏损水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>耗水量</th></tr><tr><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>小计</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>小计</th></tr></thead><tbody><tr><td rowspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>主要生产用水</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>1</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>测试水平衡测试统计表空数据</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td></tr></tbody><tfoot><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>水量合计</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td></tr><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>取水量</td><td colspan=\\\"''11\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>取水量=常规水资源量+非常规水资源量=0</td></tr><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>总用水量</td><td colspan=\\\"''11\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>总用水量=取水量+重复利用水量=0</td></tr></tfoot></table>\",\"caption\":\"企业水平衡测试统计表\"}"
    """
    html_content = info.get('content', '')
    soup = BeautifulSoup(html_content, 'html.parser')

    caption = info.get('caption', '')
    if caption:
        doc.add_paragraph(caption)

    for element in soup.find('table').find_all(recursive=False):
        if element.name == 'table':
            # --- 处理表格 (核心部分) ---
            rows_data = element.find_all('tr')
            if not rows_data:
                continue

            # 1. 确定表格的真实维度 (这是一个简化模型，更复杂的需要虚拟网格)
            # 我们先按最大列数创建表格
            max_cols = 0
            for tr in rows_data:
                max_cols = max(max_cols, len(tr.find_all(['td', 'th'])))

            # 创建Word表格
            table = doc.add_table(rows=len(rows_data), cols=max_cols)
            table.style = 'Table Grid' # 使用带边框的样式

            # 2. 填充并合并单元格
            # 创建一个 "虚拟网格" 来跟踪已经被合并的单元格
            grid = [[None for _ in range(max_cols)] for _ in range(len(rows_data))]

            for r_idx, tr in enumerate(rows_data):
                c_idx = 0
                for td in tr.find_all(['td', 'th']):
                    # 找到当前行中第一个可用的单元格
                    while grid[r_idx][c_idx] is not None:
                        c_idx += 1

                    rowspan = int(td.get('rowspan', 1))
                    colspan = int(td.get('colspan', 1))

                    # 填充文本
                    cell = table.cell(r_idx, c_idx)
                    cell.text = td.get_text()

                    # 合并单元格
                    if rowspan > 1 or colspan > 1:
                        top_left_cell = cell
                        bottom_right_cell = table.cell(r_idx + rowspan - 1, c_idx + colspan - 1)
                        top_left_cell.merge(bottom_right_cell)

                    # 在虚拟网格中标记被占用的单元格
                    for i in range(r_idx, r_idx + rowspan):
                        for j in range(c_idx, c_idx + colspan):
                            if i == r_idx and j == c_idx:
                                continue # 跳过左上角单元格本身
                            grid[i][j] = "merged" # 标记为已占用

                    c_idx += colspan
    return doc

def add_custom_simple_paragraph(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义段落.
    JSON: "{\"type\":\"SimPara\",\"useTemp\":true,\"content\":\"第一段......\",\"fontName\":\"SimSun\",\"fontSize\":10.5,\"fontBold\":false,\"fontItalic\":false,\"align\":\"Left\",\"leftIndent\":2,\"lineSpacing\":1.5,\"spaceBefore\":0.5,\"spaceAfter\":0.5}"
    注意:
    leftIndent: 缩进值, 配置的是与word设置相同的xx个字符数, 例如2字符.
    lineSpacing: 行距, 配置的是与word设置相同的xx倍行距, 例如1.5倍行距.
    spaceBefore: 段前, 配置的是与word设置相同的段前xx行, 例如0.5行.
    spaceAfter: 段后, 配置的是与word设置相同的段前xx行, 例如0.5行.
    """
    p = doc.add_paragraph()
    text = info.get('content', '')
    run = p.add_run(text)
    if info.get('useTemp', False):
        pass
    else:
        font_name = info.get('fontName', 'SimSun')
        font_size = normalize_font_size(info.get('fontSize', 10.5))
        font_bold = info.get('fontBold', False)
        font_italic = info.get('fontItalic', False)
        font_color = info.get('fontColor', '000000')  # 默认黑色
        align = align_map.get(info.get('align', 'left').upper(), WD_ALIGN_PARAGRAPH.LEFT)
        left_indent = info.get('leftIndent', 2)
        line_spacing = info.get('lineSpacing', 1.0)
        space_before = info.get('spaceBefore', 0.5)
        space_after = info.get('spaceAfter', 0.5)
        # 设置段落样式
        p.alignment = align
        # 设置字体样式
        run.font.name = font_name
        run.font.size = font_size
        run.font.bold = font_bold
        run.font.italic = font_italic
        # 设置字体颜色
        if font_color:
            color_obj = parse_font_color(font_color)
            if color_obj:
                run.font.color.rgb = color_obj
        # 设置东亚字体以确保中文字符正确显示
        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        # 设置段落缩进和间距
        p.paragraph_format.left_indent  = int(0.6 * left_indent * font_size)  # 设置左缩进（字符数 * 字体大小 * 0.6，0.6是中文字符宽度系数）
        p.paragraph_format.line_spacing = line_spacing
        p.paragraph_format.space_before = int(1.2 * space_before * font_size) # 设置段前间距（行数 * 字体大小 * 1.2，1.2是行高系数）
        p.paragraph_format.space_after  = int(1.2 * space_after * font_size)  # 设置段后间距（行数 * 字体大小 * 1.2，1.2是行高系数）

    return doc

def add_html_node(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    Note: it failed to parse the whole html node. error: list index out of range.
    添加HTML节点.
    注意: 由于是调用html4docx库解析html节点, 细节处不能完全控制.
    JSON: "{\"type\":\"HTML\",\"content\":\"<table class=\\\"''table-to-export\\\"'' style=\\\"''border-collapse: collapse; border: 2px solid rgb(140 140 140); font-family: sans-serif; font-size: 0.8rem; letter-spacing: 1px;\\\"'' data-tbl-name=\\\"''4.1 企业水平衡测试统计表\\\"''><thead><tr><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>用水分类</th><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>序号</th><th rowspan=\\\"''3\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>用水单元名称</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''2\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>取水量(m³/d)</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''6\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>重复利用水量(m³/d)</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他水量(m³/d)</th></tr><tr><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>常规水资源量</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>非常规水资源量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>直接冷却循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>间接循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他循环水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>蒸汽冷凝回用量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>回用水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>其他串联水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>排水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>漏损水量</th><th rowspan=\\\"''2\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>耗水量</th></tr><tr><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>小计</th><th rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;;\\\"''>小计</th></tr></thead><tbody><tr><td rowspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>主要生产用水</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>1</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>测试水平衡测试统计表空数据</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td rowspan=\\\"''1\\\"'' colspan=\\\"''1\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td></tr></tbody><tfoot><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>水量合计</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td><td style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>0</td></tr><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>取水量</td><td colspan=\\\"''11\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>取水量=常规水资源量+非常规水资源量=0</td></tr><tr><td colspan=\\\"''3\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>总用水量</td><td colspan=\\\"''11\\\"'' style=\\\"''border: 1px solid rgb(160 160 160); padding: 8px 10px;\\\"''>总用水量=取水量+重复利用水量=0</td></tr></tfoot></table>\",\"caption\":\"企业水平衡测试统计表\"}"
    """
    html_content = info.get('content', '')
    parser = HtmlToDocx()
    parser.add_html_to_document(html_content, doc)

    return doc

def add_custom_cover(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加封面.
    JSON: "[{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"type\":\"Title\",\"useTemp\":false,\"content\":\"水平衡表\",\"fontName\":\"SimSun\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false},{\"type\":\"SubTitle\",\"useTemp\":false,\"content\":\"神兴小学\",\"fontName\":\"SimHei\",\"fontSize\":20,\"fontBold\":true,\"fontItalic\":false},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"二号\"}]"
    """
    for each in info:
        if isinstance(each, list):
            parse_composite_element(doc, each)
        elif isinstance(each, dict):
            parse_simple_element(doc, each)
    #add_custom_new_page(doc, {}) # 由文档描述决定是否换页
    return doc

def add_custom_toc(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义目录.
    JSON: "[{\"type\":\"Heading\",\"useTemp\":true,\"content\":\"目录\",\"level\":1,\"fontName\":\"SimHei\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}]"
    """
    for each in info:
        if isinstance(each, list):
            parse_composite_element(doc, each)
        elif isinstance(each, dict):
            parse_simple_element(doc, each)
    add_toc(doc)
    #add_custom_new_page(doc, {}) # 由文档描述决定是否换页
    return doc

def add_custom_chapter(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义章.
    JSON: "[{\"type\":\"Heading\",\"useTemp\":true,\"content\":\"目录\",\"level\":1,\"fontName\":\"SimHei\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}]"
    """
    for each in info:
        if isinstance(each, list):
            parse_composite_element(doc, each)
        elif isinstance(each, dict):
            parse_simple_element(doc, each)
    #add_custom_new_page(doc, {}) # 由文档描述决定是否换页
    return doc

def add_custom_section(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加自定义节.
    JSON: "[{\"type\":\"Heading\",\"useTemp\":true,\"content\":\"目录\",\"level\":1,\"fontName\":\"SimHei\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}]"
    """
    for each in info:
        if isinstance(each, list):
            parse_composite_element(doc, each)
        elif isinstance(each, dict):
            parse_simple_element(doc, each)
    return doc

def add_custom_files(doc: DocumentObject, info: dict) -> DocumentObject:
    """
    添加文件列表.
    JSON: "[{\"type\":\"Heading\",\"useTemp\":true,\"content\":\"目录\",\"level\":1,\"fontName\":\"SimHei\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false}]"
    """
    for each in info:
        if isinstance(each, list):
            parse_composite_element(doc, each)
        elif isinstance(each, dict):
            parse_simple_element(doc, each)
    return doc

def parse_composite_element(doc: DocumentObject, content: list) -> DocumentObject:
    """
    解析复合元素.
    """
    content_type = content[0]
    content_info = content[1]
    if content_type.lower() == "cover":
        add_custom_cover(doc, content_info)
    elif content_type.lower() == "toc":
        add_custom_toc(doc, content_info)
    elif content_type.lower() == "chapter":
        add_custom_chapter(doc, content_info)
    elif content_type.lower() == "section":
        add_custom_section(doc, content_info)
    elif content_type.lower() == "files":
        add_custom_files(doc, content_info)
    else:
        logger.warning(f"未知的复合元素类型: {content_type}")
        #raise ValueError(f"未知的复合元素类型: {content_type}")
    return doc

def parse_simple_element(doc: DocumentObject, content: dict) -> DocumentObject:
    """
    解析简单元素.
    """
    # 兼容Type和type两种字段名，以及大小写
    content_type = content.get('type') or content.get('Type')
    content_info = content
    if content_type and content_type.lower() == "enter":
        add_custom_enter(doc, content_info)
    elif content_type and content_type.lower() == "newpage":
        add_custom_new_page(doc, content_info)
    elif content_type and content_type.lower() == "title":
        add_custom_title(doc, content_info)
    elif content_type and content_type.lower() == "subtitle":
        add_custom_subtitle(doc, content_info)
    elif content_type and content_type.lower() == "heading":
        add_custom_heading(doc, content_info)
    elif content_type and content_type.lower() == "simpara":
        add_custom_simple_paragraph(doc, content_info)
    elif content_type and content_type.lower() == "figure":
        add_custom_figure(doc, content_info)
    elif content_type and content_type.lower() == "pdfimg":
        add_custom_pdf_img(doc, content_info)
    elif content_type and content_type.lower() == "table":
        add_custom_table(doc, content_info)
    elif content_type and content_type.lower() == "html":
        parse_html_dom(doc, content_info)
    else:
        logger.warning(f"未知的简单元素类型: {content_type}")
        #raise ValueError(f"未知的简单元素类型: {content_type}")
    return doc

def parse_docx_design(doc: DocumentObject, contents: list) -> DocumentObject:
    """
    解析文档中的元素.

    contents: 数组, 文档元素描述, contents数组每个成员都描述了一种复合元素或者一个简单元素,
    复合元素是一个数组, 第一个元素为元素类型(根据元素类型分发到不同的处理函数), 第二个元素为元素内容(根据元素类型不同, 内容格式也不同, 元素内容可以嵌套为数组).
    简单元素是一个字典(由js对象解析过来). 字典包含一个type键, 表示简单元素的类型, 到对应的函数去处理; 字典的content是简单元素的内容, 通常是字符串, 有时也可以没有, 例如换页元素; 字典其它键用于元素的样式等设置.

    元素类型:
    # 复合元素 := [type, [info]]
    包含以下几种type:
    - Cover: 封面
    - Toc: 目录
    - Files: 附件列表(图像或pdf文件)
    - Chapter: 章节(暂未实现)

    # 简单元素 := info: dict
    info字典的type键表示简单元素的类型, 包含以下几种type:
    - Enter: 换行
    - NewPage: 换页
    - Title: 标题
    - SubTitle: 副标题
    - Heading: 章节标题
    - SimPara: 简单的段落, 即纯文字内容的段落.
    - Image: 图片
    - Table: 表格

    例子:
    [
        ["Cover", [{"Type":"enter","useTemp":false,"fontSize":"小一"},{"Type":"enter","useTemp":false,"fontSize":"小一"},{"Type":"enter","useTemp":false,"fontSize":"小一"},{"Type":"enter","useTemp":false,"fontSize":"小一"},{"type":"title","useTemp":false,"content":"水平衡表","fontName":"SimSun","fontSize":32,"fontBold":true,"fontItalic":false},{"type":"subTitle","useTemp":false,"content":"神兴小学","fontName":"SimHei","fontSize":20,"fontBold":true,"fontItalic":false},{\"Type\":\"NewPage\"}]],
    ],

    """
    for content in contents:
        if isinstance(content, list):
            parse_composite_element(doc, content)
        elif isinstance(content, dict):
            parse_simple_element(doc, content)
        else:
            raise ValueError(f"未知的元素类型: {content}")
    return doc
