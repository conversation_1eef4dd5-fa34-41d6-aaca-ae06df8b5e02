# -*- coding: utf-8 -*-

import multiprocessing
from typing import Any, Dict, Iterator, Type, TypeVar, Optional, Tuple, KeysView, ValuesView, ItemsView

# 定义一个类型变量, 用于泛型编程, 使得类型提示更准确
KT = TypeVar('KT') # Key Type
VT = TypeVar('VT') # Value Type

class ProcessSafeDict(object):
    """
    一个进程安全的哈希表(字典)实现, 其接口与 Python 内置的 dict 类型完全兼容.

    背景:
    当 FastAPI 与 uvicorn 一起并使用多个 workers (--workers > 1) 运行时, uvicorn 会创建多个独立的 Python 进程.
    标准的 Python 字典 (dict) 是存在于每个进程各自的内存空间中的, 它们之间不能直接共享数据.
    为了在这些独立的 worker 进程之间共享和同步状态, 我们需要一个进程安全的机制.

    实现原理:
    本类使用 Python 的 `multiprocessing.Manager` 来创建一个共享的字典.
    Manager 会启动一个独立的管理进程, 该进程维护一个真实的字典对象.
    所有其他的 worker 进程通过一个特殊的代理对象来访问和修改这个由 Manager 维护的字典.
    Manager 内部处理了所有必要的进程间通信和锁定, 从而确保了操作的原子性和数据的一致性.
    
    这个类将 Manager 创建的代理字典封装起来, 并提供了与标准 dict 完全相同的方法,
    使其可以作为标准 dict 的直接替代品.
    """

    def __init__(self, *args, **kwargs):
        """
        初始化方法, 与 dict 的初始化方法行为一致.
        可以接受一个可迭代对象, 或者关键字参数来初始化字典.
        """
        # 创建一个 multiprocessing 管理器实例.
        # 这个管理器负责在后台启动一个服务进程, 用于托管共享对象.
        self._manager = multiprocessing.Manager()

        # 使用管理器创建一个进程安全的字典代理对象.
        # 所有对 self._proxy_dict 的操作都会通过进程间通信(IPC)发送到管理器进程.
        # 管理器进程会安全地对真实字典进行操作, 并返回结果.
        self._proxy_dict = self._manager.dict(*args, **kwargs)

    # --- 核心魔法方法, 确保基本操作 (如 [], len, in) 的兼容性 ---

    def __setitem__(self, key: KT, value: VT) -> None:
        """
        实现字典的赋值操作, 例如: d['key'] = 'value'.
        这个操作会通过代理对象在管理进程中执行.
        """
        self._proxy_dict[key] = value

    def __getitem__(self, key: KT) -> VT:
        """
        实现字典的取值操作, 例如: d['key'].
        如果键不存在, 会像标准字典一样抛出 KeyError.
        """
        return self._proxy_dict[key]

    def __delitem__(self, key: KT) -> None:
        """
        实现字典的删除操作, 例如: del d['key'].
        """
        del self._proxy_dict[key]

    def __len__(self) -> int:
        """
        返回字典中元素的数量, 例如: len(d).
        """
        return len(self._proxy_dict)

    def __iter__(self) -> Iterator[KT]:
        """
        返回字典键的迭代器, 例如: for key in d:.
        """
        return iter(self._proxy_dict)

    def __contains__(self, key: object) -> bool:
        """
        检查键是否存在于字典中, 例如: 'key' in d.
        """
        return key in self._proxy_dict

    def __str__(self) -> str:
        """
        返回字典的字符串表示形式, 例如: str(d).
        """
        # 注意: repr(self._proxy_dict) 会给出代理对象的详细信息, 而不是像普通dict那样.
        # 为了获得与普通dict一致的输出, 我们需要将其转换为普通dict.
        return str(dict(self._proxy_dict.items()))
        

    def __repr__(self) -> str:
        """
        返回字典的官方字符串表示, 用于调试, 例如: print(d).
        """
        return repr(dict(self._proxy_dict.items()))

    # --- 常用 dict 方法的实现, 直接调用代理对象的同名方法 ---

    def get(self, key: KT, default: Optional[Any] = None) -> Optional[VT]:
        """
        获取指定键的值, 如果键不存在则返回默认值.
        """
        return self._proxy_dict.get(key, default)

    def keys(self) -> KeysView[KT]:
        """
        返回一个包含字典所有键的视图对象.
        """
        return self._proxy_dict.keys()

    def values(self) -> ValuesView[VT]:
        """
        返回一个包含字典所有值的视图对象.
        """
        return self._proxy_dict.values()

    def items(self) -> ItemsView[KT, VT]:
        """
        返回一个包含字典所有 (键, 值) 元组的视图对象.
        """
        return self._proxy_dict.items()

    def pop(self, key: KT, default: Optional[Any] = None) -> Optional[VT]:
        """
        删除指定的键, 并返回其对应的值. 如果键不存在, 返回默认值.
        """
        return self._proxy_dict.pop(key, default)

    def popitem(self) -> Tuple[KT, VT]:
        """
        删除并返回字典中的最后一对 (键, 值).
        """
        return self._proxy_dict.popitem()

    def clear(self) -> None:
        """
        清空字典中的所有项.
        """
        self._proxy_dict.clear()

    def update(self, *args, **kwargs) -> None:
        """
        使用另一个字典或键值对迭代器来更新当前字典.
        """
        self._proxy_dict.update(*args, **kwargs)

    def setdefault(self, key: KT, default: Optional[VT] = None) -> Optional[VT]:
        """
        如果键存在, 返回其值. 如果不存在, 插入值为 default 的键, 并返回 default.
        """
        return self._proxy_dict.setdefault(key, default)
        
    def copy(self) -> Dict[KT, VT]:
        """
        返回字典的一个浅拷贝. 这是一个普通的 dict, 而不是 ProcessSafeDict.
        """
        return self._proxy_dict.copy()


if __name__ == '__main__':
    # --- 一个简单的使用和测试示例 ---
    # 这个示例展示了 ProcessSafeDict 的行为与普通 dict 完全一致.
    # 在 FastAPI 应用中, 你只需要在全局范围内创建一次这个对象.
    
    print("--- 开始测试 ProcessSafeDict ---")

    # 1. 初始化
    shared_tasks = ProcessSafeDict({'task:001': 'pending'})
    print(f"初始化后: {shared_tasks}")

    # 2. 赋值 (__setitem__)
    shared_tasks['task:002'] = 'running'
    shared_tasks['task:003'] = 'pending'
    print(f"赋值后: {shared_tasks}")

    # 3. 取值 (__getitem__)
    status = shared_tasks['task:002']
    print(f"获取 task:002 的状态: {status}")

    # 4. 长度 (__len__)
    print(f"当前任务数量: {len(shared_tasks)}")

    # 5. 成员测试 (__contains__)
    print(f"'task:001' 是否存在? {'task:001' in shared_tasks}")
    print(f"'task:999' 是否存在? {'task:999' in shared_tasks}")

    # 6. 使用 get 方法
    print(f"使用 get 获取 task:003: {shared_tasks.get('task:003')}")
    print(f"使用 get 获取不存在的 task:999: {shared_tasks.get('task:999', 'not_found')}")

    # 7. 迭代 (__iter__)
    print("遍历所有任务ID:")
    for task_id in shared_tasks:
        print(f"  - {task_id}")

    # 8. items() 方法
    print("遍历所有任务项 (items):")
    for task_id, status in shared_tasks.items():
        print(f"  - {task_id}: {status}")

    # 9. 删除 (__delitem__)
    del shared_tasks['task:001']
    print(f"删除 task:001 后: {shared_tasks}")

    # 10. pop() 方法
    popped_value = shared_tasks.pop('task:002')
    print(f"Pop 'task:002', 值为: {popped_value}. 剩余: {shared_tasks}")

    # 11. update() 方法
    shared_tasks.update({'task:004': 'completed', 'task:005': 'failed'})
    print(f"Update 后: {shared_tasks}")

    # 12. clear() 方法
    shared_tasks.clear()
    print(f"Clear 后: {shared_tasks}")
    
    print("--- 测试结束 ---")