# HTML解析功能说明

## 概述

本项目新增了HTML到DOCX的转换功能，通过`parse_html_dom`函数实现HTML节点解析并转换为Word文档格式。该功能已集成到`utils.py`的`parse_simple_element`函数中，支持通过JSON配置的方式使用。

## 功能特性

### 支持的HTML标签

- **标题标签**: `h1`, `h2`, `h3`, `h4`, `h5`, `h6`
- **段落标签**: `p`, `div`
- **列表标签**: `ul` (无序列表), `ol` (有序列表), `li` (列表项)
- **表格标签**: `table`, `tr`, `th`, `td` (支持合并单元格)
- **图片标签**: `img` (自动下载并插入)
- **图形标签**: `figure`, `figcaption`

### 自动跳过的标签

以下标签会被自动跳过，不会影响文档生成：
- `script` - 脚本标签
- `button` - 按钮标签
- `style` - 样式标签
- `meta` - 元数据标签
- `link` - 链接标签
- `head` - 头部标签
- `title` - 标题标签

### 支持的样式属性

- **字体名称** (`fontName`): 设置文本字体，默认为"SimSun"
- **字体大小** (`fontSize`): 支持数字(磅)或中文字号(如"小四")
- **字体加粗** (`fontBold`): 布尔值，设置文本是否加粗
- **字体斜体** (`fontItalic`): 布尔值，设置文本是否斜体
- **对齐方式** (`align`): "Left", "Center", "Right", "Justify"
- **左缩进** (`leftIndent`): 数字，表示缩进字符数
- **行距** (`lineSpacing`): 数字，表示行距倍数
- **段前间距** (`spaceBefore`): 数字，表示段前行数
- **段后间距** (`spaceAfter`): 数字，表示段后行数

### 图片处理

- 自动下载网络图片并插入文档
- 支持本地域名映射：本地路径(如"/static/xxx.png")会自动转换为"http://127.0.0.1:5579/static/xxx.png"
- 图片下载失败时会插入占位文本
- 默认图片宽度为5英寸

### 表格处理

- 支持复杂表格结构
- 支持单元格合并(`rowspan`, `colspan`)
- 自动应用表格边框样式
- 使用虚拟网格算法处理合并单元格

## 使用方法

### 基本用法

```python
from docx import Document
from app.gendoc.utils import parse_simple_element

# 创建文档
doc = Document()

# HTML配置
html_config = {
    "type": "HTML",
    "content": "<div><h1>标题</h1><p>段落内容</p></div>",
    "fontName": "SimSun",
    "fontSize": 12,
    "fontBold": False,
    "fontItalic": False,
    "align": "Left",
    "leftIndent": 0,
    "lineSpacing": 1.0,
    "spaceBefore": 0,
    "spaceAfter": 0
}

# 解析HTML
doc = parse_simple_element(doc, html_config)

# 保存文档
doc.save("output.docx")
```

### JSON格式示例

```json
{
    "type": "HTML",
    "content": "<div>\n  <h1>标题</h1>\n  <p>这是一个段落</p>\n  <ul>\n    <li>列表项1</li>\n    <li>列表项2</li>\n  </ul>\n</div>",
    "fontName": "SimSun",
    "fontSize": 10.5,
    "fontBold": false,
    "fontItalic": false,
    "align": "Left",
    "leftIndent": 2,
    "lineSpacing": 1.5,
    "spaceBefore": 0.5,
    "spaceAfter": 0.5
}
```

### 复杂表格示例

```python
html_config = {
    "type": "HTML",
    "content": """
    <table border="1">
        <tr>
            <th colspan="2">表头合并</th>
            <th>普通表头</th>
        </tr>
        <tr>
            <td>单元格1</td>
            <td rowspan="2">垂直合并</td>
            <td>单元格3</td>
        </tr>
        <tr>
            <td>单元格4</td>
            <td>单元格6</td>
        </tr>
    </table>
    """,
    "fontName": "SimSun",
    "fontSize": 11
}
```

### 图片插入示例

```python
html_config = {
    "type": "HTML",
    "content": """
    <div>
        <h2>图片展示</h2>
        <figure>
            <img src="/static/image.png" alt="本地图片">
            <figcaption>图片说明</figcaption>
        </figure>
        <p>网络图片：</p>
        <img src="https://example.com/image.jpg" alt="网络图片">
    </div>
    """,
    "fontName": "SimSun",
    "fontSize": 12
}
```

## 文件结构

```
app/gendoc/
├── parse_html.py          # HTML解析核心模块
├── utils.py              # 工具函数(已集成HTML解析)
└── samples/
    └── html_usage_example.py  # 使用示例
```

## 核心函数

### `parse_html_dom(doc: DocumentObject, info: dict) -> DocumentObject`

**参数说明:**
- `doc`: python-docx的Document对象
- `info`: 包含HTML内容和样式信息的字典

**返回值:**
- 更新后的Document对象

**info字典结构:**
```python
{
    "type": "HTML",                    # 必需，标识为HTML类型
    "content": "<div>...</div>",       # 必需，HTML内容字符串
    "fontName": "SimSun",             # 可选，字体名称
    "fontSize": 12,                   # 可选，字体大小
    "fontBold": False,                # 可选，是否加粗
    "fontItalic": False,              # 可选，是否斜体
    "align": "Left",                  # 可选，对齐方式
    "leftIndent": 0,                  # 可选，左缩进
    "lineSpacing": 1.0,               # 可选，行距
    "spaceBefore": 0,                 # 可选，段前间距
    "spaceAfter": 0                   # 可选，段后间距
}
```

## 配置说明

### 本地域名配置

在`parse_html.py`中定义了本地域名常量：

```python
LOCAL_DOMAIN = "http://127.0.0.1:5579"
```

当HTML中的图片src以"/"开头时，会自动添加此域名前缀。

### 中文字号支持

支持以下中文字号：
- 初号: 42磅
- 小初: 36磅
- 一号: 26磅
- 小一: 24磅
- 二号: 22磅
- 小二: 18磅
- 三号: 16磅
- 小三: 15磅
- 四号: 14磅
- 小四: 12磅
- 五号: 10.5磅
- 小五: 9磅
- 六号: 7.5磅
- 小六: 6.5磅
- 七号: 5.5磅
- 八号: 5磅

## 错误处理

- HTML内容为空时会记录警告日志
- HTML解析失败时会在文档中插入错误信息
- 图片下载失败时会插入占位文本
- 表格合并单元格失败时会记录警告日志

## 日志记录

使用项目统一的日志系统，记录以下信息：
- INFO: HTML解析完成
- WARNING: HTML内容为空、合并单元格失败
- ERROR: HTML解析失败、图片下载失败

## 测试和示例

### 测试文件
- `test_parse_html.py`: 基本功能测试
- `app/gendoc/samples/html_usage_example.py`: 完整使用示例

### 运行测试

```bash
# 基本功能测试
python test_parse_html.py

# 使用示例
python app/gendoc/samples/html_usage_example.py
```

## 注意事项

1. **HTML内容转义**: 在JSON字符串中使用HTML时，需要正确转义特殊字符
2. **图片网络访问**: 图片下载需要网络连接，建议设置合理的超时时间
3. **表格复杂度**: 过于复杂的表格结构可能影响转换效果
4. **样式继承**: HTML的CSS样式不会被解析，只使用配置中的样式参数
5. **性能考虑**: 大量图片或复杂HTML结构可能影响转换性能

## 扩展开发

如需添加新的HTML标签支持，可以在`parse_html.py`中：

1. 在`process_html_element`函数中添加新的标签处理逻辑
2. 实现对应的处理函数（如`process_new_element`）
3. 更新`SKIP_TAGS`列表（如果需要跳过某些标签）

## 版本历史

- v1.0.0: 初始版本，支持基本HTML标签转换
- 支持的标签：div, h1-h6, p, table, img, figure, ul, ol, li
- 支持样式配置和图片下载
- 集成到utils.py的parse_simple_element函数