import requests
import json
import time

"""
节水型单位文档生成API测试客户端
用于调试/api/v1/wsinst/new接口的422错误
"""

def test_wsinst_api():
    """测试节水型单位文档生成API"""
    # API端点 - 注意：端口是5579，不是8000
    url = "http://127.0.0.1:5579/api/v1/wsinst/new"

    # 构造请求体数据（根据注释中的示例）
    payload = {
        "TaskKey": 'b0yj2n7a333b5b94',
        "Callback": 'http://127.0.0.1:5577/api/v1/aihub/wsinst/new/callback',
        "Contents": '',
        "Files": [
            {
                "name": 'b662e8de-f181-4321-ac74-5b857b2e110b',
                "url": 'http://lcmp.zixushuili.com:80/assets/proj/2025/11/b0yj2n172zuu219crd/fattach/B0YJ21764316386407.jpg',
                "mimeType": 'image/jpeg'
            },
            {
                "name": '1e3a8f6a-4de2-4dbe-882d-619b8fe2b7e0',
                "url": 'http://lcmp.zixushuili.com:80/assets/proj/2025/11/b0yj2n172zuu219crd/fattach/B0YJ21764316387165.jpg',
                "mimeType": 'image/jpeg'
            },
            {
                "name": '43f617a3-bfb8-4fc4-883c-a23594aceb84',
                "url": 'http://lcmp.zixushuili.com:80/assets/proj/2025/11/b0yj2n172zuu219crd/fattach/B0YJ21764316387272.jpg',
                "mimeType": 'image/jpeg'
            }
        ]
    }

    print("发送请求到API端点...")
    print(f"URL: {url}")
    print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")

    try:
        # 发送POST请求
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"}
        )

        # 打印响应状态码
        print(f"\n响应状态码: {response.status_code}")

        # 如果是422错误，打印详细的错误信息
        if response.status_code == 422:
            print("\n422 Unprocessable Entity - 详细错误信息:")
            error_details = response.json()
            print(json.dumps(error_details, ensure_ascii=False, indent=2))
        else:
            # 打印其他响应内容
            print(f"\n响应内容: {response.text}")

    except Exception as e:
        print(f"\n发送请求时发生错误: {str(e)}")

if __name__ == "__main__":
    print("=== 节水型单位文档生成API测试客户端 ===")
    test_wsinst_api()
