# -*- coding: utf-8 -*-
"""
文档生成API路由

处理docx文档生成相关的API请求。
"""

from fastapi import APIRouter, Request
from pydantic import BaseModel

# 导入项目内部模块
from ..services.docx_service import handle_docxgen_request
from ..core.logger import get_logger
from ..models.requests import DocxgenRequest
from ..models.responses import DocxgenResponse

# 创建路由器
router = APIRouter(
    prefix="/api/v1/docxgen",
    tags=["文档生成"],
    responses={404: {"description": "Not found"}}
)

# 获取日志记录器
logger = get_logger()


@router.post('/new')
def create_docxgen_task(request: DocxgenRequest):
    """
    生成docx文档任务端点.

    接收POST请求，创建生成docx文档任务。

    请求体格式:
    {
        "TaskKey": 字符串, 任务惟一标识符, JSON字符串: "d1fg7skT"
        "Callback":  字符串, 回调URL, JSON字符串: "http://127.0.0.1:5577/api/v1/aihub/docxgen/new/callback"
        "Contents": 数组, docx元素描述, JSON字符串: "[[\"Cover\",[{\"Type\":\"enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"type\":\"title\",\"useTemp\":false,\"content\":\"水平衡表\",\"fontName\":\"SimSun\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false},{\"type\":\"subTitle\",\"useTemp\":false,\"content\":\"神兴小学\",\"fontName\":\"SimHei\",\"fontSize\":20,\"fontBold\":true,\"fontItalic\":false},{\"Type\":\"NewPage\"}]]"
    }

    Contents: 数组, 文档元素描述, Contents数组每个成员都描述了一种复合元素或者一个简单元素,
    复合元素是一个数组, 第一个元素为元素类型(根据元素类型分发到不同的处理函数), 第二个元素为元素内容(根据元素类型不同, 内容格式也不同, 元素内容可以嵌套为数组).
    简单元素是一个字典(由js对象解析过来). 字典包含一个type键, 表示简单元素的类型, 到对应的函数去处理; 字典的content是简单元素的内容, 通常是字符串, 有时也可以没有, 例如换页元素; 字典其它键用于元素的样式等设置.

    元素类型:
    # 复合元素 := [type, [info]]
    包含以下几种type:
    - Cover: 封面
    - Toc: 目录
    - Chapter: 章节(暂未实现)

    # 简单元素 := info: dict
    info字典的type键表示简单元素的类型, 包含以下几种type:
    - Enter: 换行
    - NewPage: 换页
    - Title: 标题
    - SubTitle: 副标题
    - Heading: 章节标题
    - SimPara: 简单的段落, 即纯文字内容的段落.
    - Image: 图片
    - Table: 表格

    请求体完整例子(JSON str):
    "{\"TaskKey\":\"testtaskkey00\",\"Callback\":\"http://127.0.0.1:5577/api/v1/aihub/docxgen/new/callback\",\"Contents\":[\"Cover\",[{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小一\"},{\"type\":\"Title\",\"useTemp\":false,\"content\":\"水平衡表\",\"fontName\":\"SimSun\",\"fontSize\":32,\"fontBold\":true,\"fontItalic\":false},{\"type\":\"SubTitle\",\"useTemp\":false,\"content\":\"神兴小学\",\"fontName\":\"SimHei\",\"fontSize\":20,\"fontBold\":true,\"fontItalic\":false},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"二号\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"二号\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"二号\"},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"二号\"},{\"type\":\"SimPara\",\"useTemp\":false,\"content\":\"用水单位\",\"fontName\":\"SimSun\",\"fontSize\":10.5,\"fontBold\":false,\"fontItalic\":false,\"align\":\"Left\",\"leftIndent\":2,\"lineSpacing\":1.5,\"spaceBefore\":0.5,\"spaceAfter\":0.5},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小三\"},{\"type\":\"SimPara\",\"useTemp\":false,\"content\":\"填表日期\",\"fontName\":\"SimSun\",\"fontSize\":10.5,\"fontBold\":false,\"fontItalic\":false,\"align\":\"Left\",\"leftIndent\":2,\"lineSpacing\":1.5,\"spaceBefore\":0.5,\"spaceAfter\":0.5},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小三\"},{\"type\":\"SimPara\",\"useTemp\":false,\"content\":\"填表人\",\"fontName\":\"SimSun\",\"fontSize\":10.5,\"fontBold\":false,\"fontItalic\":false,\"align\":\"Left\",\"leftIndent\":2,\"lineSpacing\":1.5,\"spaceBefore\":0.5,\"spaceAfter\":0.5},{\"Type\":\"Enter\",\"useTemp\":false,\"fontSize\":\"小三\"},{\"type\":\"SimPara\",\"useTemp\":false,\"content\":\"联系人\",\"fontName\":\"SimSun\",\"fontSize\":10.5,\"fontBold\":false,\"fontItalic\":false,\"align\":\"Left\",\"leftIndent\":2,\"lineSpacing\":1.5,\"spaceBefore\":0.5,\"spaceAfter\":0.5},{\"Type\":\"NewPage\"}]]}"

    Returns:
        JSON响应: 任务创建结果
    {
        "TaskKey": "d1fg7skT",       // 任务惟一标识符
        "RuntimeStatus": "SUCCESS",  // 程序运行状态, "SUCCESS", "FAILED"
        "TaskStatus": "CREATED",     // 任务状态: 'CREATED', 'COMPLETED', 'ONGOING'
    }

    注一: 这个路由使用同步函数, FastAPI会将任务放到线程池中执行, 避免阻塞事件循环.

    注二: 这里使用Pydantic模型和依赖注入来处理请求数据验证和转换, 避免在同步函数中调用`request.json()`这个异步方法.
        **FastAPI会自动完成以下工作**:
        1. 异步地读取请求body
        2. 解析JSON
        3. 验证数据是否符合Item模型的结构和类型
        4. 如果验证失败，自动返回一个详细的422错误响应
        5. 将验证通过的数据转换成一个DocxgenRequest对象，注入到你的函数中

        **这种方法的步骤是**:
        1. 定义一个Pydantic模型, 用于定义请求体的结构和数据类型.
        2. 在路由函数中, 使用该模型作为参数, FastAPI会自动从请求体中提取数据并进行验证.
        3. 如果验证成功, 则将数据转换为模型实例, 并将其作为参数传递给处理函数.
        4. 如果验证失败, 则FastAPI会自动返回422 Unprocessable Entity响应, 并包含验证错误信息.

    """
    logger.info("创建docx文档任务")

    request_body = {"TaskKey": request.TaskKey, "Callback": request.Callback, "Contents": request.Contents}
    return handle_docxgen_request(request_body)
