version: '3.8'

services:
  aihub-server:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: aihub-server
    restart: unless-stopped
    ports:
      - "5579:5579"
    environment:
      # 服务器配置
      - AIHUB_HOST=0.0.0.0
      - AIHUB_PORT=5579
      - AIHUB_THREADS=4
      - FLASK_ENV=production

      # 环境配置
      - AIHUB_ENVIRONMENT=${AIHUB_ENVIRONMENT:-production}

      # Gunicorn配置
      - GUNICORN_WORKERS=${GUNICORN_WORKERS:-4}
      - GUNICORN_TIMEOUT=${GUNICORN_TIMEOUT:-120}
      - GUNICORN_BIND=0.0.0.0:5579

      # 日志配置
      - LOG_DIR=/app/logs
      - LOG_LEVEL=INFO
      - LOG_BACKUP_COUNT=30

      # 阿里云凭证 (请在.env文件中配置)
      - ALIBABA_CLOUD_ACCESS_KEY_ID=${ALIBABA_CLOUD_ACCESS_KEY_ID}
      - ALIBABA_CLOUD_ACCESS_KEY_SECRET=${ALIBABA_CLOUD_ACCESS_KEY_SECRET}
      - ALIBABA_CLOUD_REGION_ID=${ALIBABA_CLOUD_REGION_ID:-cn-beijing}

    volumes:
      # 持久化日志
      - aihub-logs:/app/logs
      - ./logs:/app/logs
      # 配置文件挂载
      - ./config:/app/config:ro

    networks:
      - aihub-network

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5579/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp

  # 可选: Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: aihub-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    networks:
      - aihub-network
    depends_on:
      - aihub-server
    profiles:
      - with-nginx

volumes:
  aihub-logs:
    driver: local

networks:
  aihub-network:
    driver: bridge