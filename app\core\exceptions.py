# -*- coding: utf-8 -*-
"""
异常处理器

定义FastAPI应用的全局异常处理器。
"""

from fastapi import Request
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from ..utils.http import create_error_response
from .logger import get_logger

# 获取日志记录器
logger = get_logger()


async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """
    HTTP异常处理器

    Args:
        request: 请求对象
        exc: HTTP异常对象

    Returns:
        JSONResponse: 错误响应
    """
    if exc.status_code == 404:
        return create_error_response("接口不存在", 404)
    elif exc.status_code == 405:
        return create_error_response("请求方法不被允许", 405)
    else:
        return create_error_response(str(exc.detail), exc.status_code)


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    请求验证异常处理器

    Args:
        request: 请求对象
        exc: 验证异常对象

    Returns:
        JSONResponse: 错误响应
    """
    # 获取详细的错误信息
    error_details = exc.errors()
    
    # 记录详细的验证错误日志
    logger.error("="*60)
    logger.error("请求参数验证失败 (422 Unprocessable Entity)")
    logger.error(f"请求URL: {request.url}")
    logger.error(f"请求方法: {request.method}")
    
    # 尝试记录请求体（如果有）
    try:
        if hasattr(exc, 'body'):
            logger.error(f"请求体: {exc.body}")
    except Exception:
        pass
        
    logger.error("错误详情:")
    for error in error_details:
        loc = " -> ".join([str(x) for x in error.get('loc', [])])
        msg = error.get('msg', '')
        type_ = error.get('type', '')
        logger.error(f"  - 字段: {loc}")
        logger.error(f"    错误: {msg}")
        logger.error(f"    类型: {type_}")
        if 'ctx' in error:
            logger.error(f"    上下文: {error['ctx']}")
    logger.error("="*60)

    return create_error_response(f"请求参数验证失败: {str(exc)}", 422, data=error_details)


async def general_exception_handler(request: Request, exc: Exception):
    """
    通用异常处理器

    Args:
        request: 请求对象
        exc: 异常对象

    Returns:
        JSONResponse: 错误响应
    """
    logger.error(f"服务器内部错误: {str(exc)}")
    return create_error_response("服务器内部错误", 500)