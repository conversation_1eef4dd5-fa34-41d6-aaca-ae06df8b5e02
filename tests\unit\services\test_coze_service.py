# -*- coding: utf-8 -*-
"""
测试coze_service.py中的各个函数

这个脚本用于测试coze_service.py中的主要函数：
- upload_file: 文件上传功能
- call_chat_agent: 智能体调用功能
- peek_conversion_status: 查询对话状态功能（新返回格式）
- check_conversion_completed: 轮询对话完成状态功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.append(project_root)

from app.services.coze_service import upload_file, call_chat_agent, peek_conversion_status, check_conversion_completed

async def test_upload_file():
    """
    测试upload_file函数
    """
    # 测试参数
    token = "sat_cjVlORG4J9x54NomG9fm9RKm0GXoXQ40QL1XEV22fm1XOK31inZLBmHdWDcwScnZ"
    # 使用相对路径指向测试数据文件
    test_data_dir = os.path.join(project_root, 'tests', 'test_data')
    file_path = os.path.join(test_data_dir, 'bo9fe2f7dc0fbdbc.docx')

    print(f"开始测试upload_file函数...")
    print(f"Token: {token[:20]}...")
    print(f"文件路径: {file_path}")

    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return

        print(f"文件存在，开始上传...")

        # 调用upload_file函数
        result = await upload_file(token, file_path)

        if result:
            print(f"✅ 文件上传成功!")
            print(f"文件类型: {result.get('type')}")
            print(f"文件ID: {result.get('file_id')}")
            print(f"完整结果: {result}")
        else:
            print(f"❌ 文件上传失败: 未返回结果")

    except FileNotFoundError as e:
        print(f"❌ 文件不存在错误: {e}")
    except Exception as e:
        print(f"❌ 上传过程中发生错误: {e}")

async def test_call_chat_agent():
    """
    测试call_chat_agent函数
    """
    # 测试参数
    token = "pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo"
    bot_id = "7541317373962747945"
    user_id = "lcmp123456789"

    print(f"开始测试call_chat_agent函数...")
    print(f"Token: {token[:20]}...")
    print(f"Bot ID: {bot_id}")
    print(f"User ID: {user_id}")

    try:
        # 首先上传两个文件获取files_list
        test_data_dir = os.path.join(project_root, 'tests', 'test_data')
        file1_path = os.path.join(test_data_dir, '简介.docx')
        file2_path = os.path.join(test_data_dir, '人数和水量.jpg')

        print(f"正在上传第一个文件: {file1_path}")
        result1 = await upload_file(token, file1_path)
        if not result1:
            print(f"❌ 第一个文件上传失败")
            return
        print(f"✅ 第一个文件上传成功: {result1}")

        print(f"正在上传第二个文件: {file2_path}")
        result2 = await upload_file(token, file2_path)
        if not result2:
            print(f"❌ 第二个文件上传失败")
            return
        print(f"✅ 第二个文件上传成功: {result2}")

        # 构建files_list
        files_list = [
            {"type": "file", "file_id": result1.get('file_id')},
            {"type": "file", "file_id": result2.get('file_id')}
        ]

        print(f"Files list: {files_list}")
        print(f"开始调用智能体...")

        # 调用call_chat_agent函数
        chat_result = await call_chat_agent(token, bot_id, user_id, files_list)
        
        if chat_result:
            print(f"✅ 智能体调用成功!")
            print(f"Conversation ID: {chat_result.get('conversation_id')}")
            print(f"Chat ID: {chat_result.get('id')}")
            print(f"完整结果: {chat_result}")
            
            # 验证返回结果的结构
            assert isinstance(chat_result, dict), "返回结果应该是字典类型"
            assert 'conversation_id' in chat_result, "返回结果应该包含conversation_id字段"
            assert 'id' in chat_result, "返回结果应该包含id字段"
            assert chat_result.get('conversation_id'), "conversation_id不能为空"
            assert chat_result.get('id'), "id不能为空"
            print(f"✅ 返回结果验证通过!")
        else:
            print(f"❌ 智能体调用失败: 未返回结果")

    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_peek_conversion_status():
    """
    测试peek_conversion_status函数
    """
    # 测试参数
    token = "pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo"
    conversation_id = "test_conversation_id"
    chat_id = "test_chat_id"

    print(f"开始测试peek_conversion_status函数...")
    print(f"Token: {token[:20]}...")
    print(f"Conversation ID: {conversation_id}")
    print(f"Chat ID: {chat_id}")

    try:
        # 调用peek_conversion_status函数
        result = await peek_conversion_status(token, conversation_id, chat_id)
        
        if result:
            print(f"✅ 查询对话状态成功!")
            print(f"状态: {result.get('status')}")
            print(f"完整结果: {result}")
            
            # 验证返回结果的结构
            assert isinstance(result, dict), "返回结果应该是字典类型"
            assert 'status' in result, "返回结果应该包含status字段"
            assert 'result' in result, "返回结果应该包含result字段"
            print(f"✅ 返回结果验证通过!")
        else:
            print(f"❌ 查询对话状态失败: 未返回结果")

    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        # 这是预期的，因为我们使用的是测试参数
        print(f"ℹ️ 这是预期的异常，因为使用了测试参数")


async def test_check_conversion_completed():
    """
    测试check_conversion_completed函数
    """
    # 测试参数
    token = "pat_atGSG7ByTHPEBPDfSjFdhcucsGoGJBOvt9H70k73qjiH6GkYNrRoNjMwvpeBQQmo"
    conversation_id = "test_conversation_id"
    chat_id = "test_chat_id"

    print(f"开始测试check_conversion_completed函数...")
    print(f"Token: {token[:20]}...")
    print(f"Conversation ID: {conversation_id}")
    print(f"Chat ID: {chat_id}")

    # 定义回调函数
    callback_called = False
    error_callback_called = False
    error_exception = None

    def success_callback():
        nonlocal callback_called
        callback_called = True
        print("✅ [回调] 成功回调被调用")

    def error_callback(exception: Exception):
        nonlocal error_callback_called, error_exception
        error_callback_called = True
        error_exception = exception
        print(f"❌ [回调] 错误回调被调用: {str(exception)}")

    try:
        # 调用check_conversion_completed函数，设置短超时
        await check_conversion_completed(
            token=token,
            conversation_id=conversation_id,
            chat_id=chat_id,
            interval=2,  # 2秒间隔
            timeout=5,   # 5秒超时
            callback=success_callback,
            errback=error_callback
        )
        
        print(f"✅ check_conversion_completed函数执行完成")
        print(f"成功回调调用: {callback_called}")
        print(f"错误回调调用: {error_callback_called}")
        if error_exception:
            print(f"错误信息: {str(error_exception)}")

    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        print(f"ℹ️ 这是预期的异常，因为使用了测试参数")


if __name__ == "__main__":
    # 运行upload_file测试
    print("=== 测试 upload_file 函数 ===")
    asyncio.run(test_upload_file())

    print("\n" + "="*50 + "\n")

    # 运行call_chat_agent测试
    print("=== 测试 call_chat_agent 函数 ===")
    asyncio.run(test_call_chat_agent())

    print("\n" + "="*50 + "\n")

    # 运行peek_conversion_status测试
    print("=== 测试 peek_conversion_status 函数 ===")
    asyncio.run(test_peek_conversion_status())

    print("\n" + "="*50 + "\n")

    # 运行check_conversion_completed测试
    print("=== 测试 check_conversion_completed 函数 ===")
    asyncio.run(test_check_conversion_completed())