# -*- coding: utf-8 -*-
"""
数据验证器测试模块

测试Pydantic验证器的功能，确保数据验证逻辑正确工作。
"""

import pytest
from pydantic import ValidationError
from app.models.requests import TingwuRequest, ChatRequest, validate_tingwu_request, validate_chat_request


class TestTingwuRequest:
    """千义听悟请求验证测试类"""

    def test_valid_minimal_request(self):
        """测试最小有效请求"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3"
        }
        request = validate_tingwu_request(data)
        assert request.TaskKey == "test_task_123"
        assert str(request.fileUrl) == "https://example.com/audio.mp3"

    def test_valid_full_request(self):
        """测试完整有效请求"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "sampleRate": 16000,
            "speakerCount": 2,
            "outputLevel": 2,
            "diarizationEnabled": True,
            "progressiveCallbacksEnabled": False,
            "translationEnabled": False,
            "autoChaptersEnabled": True,
            "meetingAssistanceEnabled": False,
            "summarizationEnabled": True,
            "sourceLanguage": "cn"
        }
        request = validate_tingwu_request(data)
        assert request.TaskKey == "test_task_123"
        assert request.sampleRate == 16000
        assert request.speakerCount == 2
        assert request.sourceLanguage == "cn"

    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        # 缺少TaskKey
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request({"fileUrl": "https://example.com/audio.mp3"})
        assert "TaskKey" in str(exc_info.value)

        # 缺少fileUrl
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request({"TaskKey": "test_task_123"})
        assert "fileUrl" in str(exc_info.value)

    def test_invalid_task_key_format(self):
        """测试无效的TaskKey格式"""
        data = {
            "TaskKey": "invalid@task#key",  # 包含非法字符
            "fileUrl": "https://example.com/audio.mp3"
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "String should match pattern" in str(exc_info.value)

    def test_invalid_task_key_length(self):
        """测试TaskKey长度超限"""
        data = {
            "TaskKey": "a" * 101,  # 超过100字符限制
            "fileUrl": "https://example.com/audio.mp3"
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "String should have at most 100 characters" in str(exc_info.value)

    def test_invalid_file_url(self):
        """测试无效的文件URL"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "invalid-url"  # 无效URL格式
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be a valid URL" in str(exc_info.value)

    def test_invalid_sample_rate(self):
        """测试无效的采样率"""
        # 采样率过低
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "sampleRate": 7999
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be greater than or equal to 8000" in str(exc_info.value)

        # 采样率过高
        data["sampleRate"] = 48001
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be less than or equal to 48000" in str(exc_info.value)

    def test_invalid_speaker_count(self):
        """测试无效的说话人数量"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "speakerCount": 21  # 超过最大值20
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be less than or equal to 20" in str(exc_info.value)

    def test_invalid_output_level(self):
        """测试无效的输出级别"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "outputLevel": 4  # 超过最大值3
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be less than or equal to 3" in str(exc_info.value)

    def test_invalid_source_language(self):
        """测试无效的源语言"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "sourceLanguage": "invalid_lang"  # 不支持的语言
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be" in str(exc_info.value)

    def test_invalid_boolean_fields(self):
        """测试无效的布尔字段"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3",
            "diarizationEnabled": "invalid_bool"  # 无效的布尔值
        }
        with pytest.raises(ValidationError) as exc_info:
            validate_tingwu_request(data)
        assert "Input should be a valid boolean" in str(exc_info.value)

class TestChatRequest:
    """聊天请求验证测试类"""

    def test_valid_minimal_request(self):
        """测试最小有效请求"""
        data = {
            "SysContent": "你是一个AI助手",
            "UserContent": "你好"
        }
        request = validate_chat_request(data)
        assert request.Model == "qwen-plus"  # 默认值
        assert request.SysContent == "你是一个AI助手"
        assert request.UserContent == "你好"

    def test_valid_full_request(self):
        """测试完整有效请求"""
        data = {
            "Model": "qwen-turbo",
            "SysContent": "你是一个专业的AI助手",
            "UserContent": "请介绍一下人工智能"
        }
        request = validate_chat_request(data)
        assert request.Model == "qwen-turbo"
        assert request.SysContent == "你是一个专业的AI助手"
        assert request.UserContent == "请介绍一下人工智能"

    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        # 缺少SysContent
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({"UserContent": "你好"})
        assert "SysContent" in str(exc_info.value)

        # 缺少UserContent
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({"SysContent": "你是一个AI助手"})
        assert "UserContent" in str(exc_info.value)

    def test_empty_content_fields(self):
        """测试空内容字段"""
        # 空的SysContent
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({
                "SysContent": "",
                "UserContent": "你好"
            })
        assert "String should have at least 1 character" in str(exc_info.value)

        # 空的UserContent
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({
                "SysContent": "你是一个AI助手",
                "UserContent": ""
            })
        assert "String should have at least 1 character" in str(exc_info.value)

    def test_whitespace_only_content(self):
        """测试仅包含空白字符的内容"""
        # 仅空格的SysContent
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({
                "SysContent": "   ",
                "UserContent": "你好"
            })
        # 自定义验证器会去除空白字符后检查

    def test_content_length_limits(self):
        """测试内容长度限制"""
        # SysContent过长
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({
                "SysContent": "a" * 2001,  # 超过2000字符限制
                "UserContent": "你好"
            })
        assert "String should have at most 2000 characters" in str(exc_info.value)

        # UserContent过长
        with pytest.raises(ValidationError) as exc_info:
            validate_chat_request({
                "SysContent": "你是一个AI助手",
                "UserContent": "a" * 4001  # 超过4000字符限制
            })
        assert "String should have at most 4000 characters" in str(exc_info.value)


class TestValidationFunctions:
    """验证函数测试类"""

    def test_validate_tingwu_request_function(self):
        """测试千义听悟请求验证函数"""
        data = {
            "TaskKey": "test_task_123",
            "fileUrl": "https://example.com/audio.mp3"
        }
        request = validate_tingwu_request(data)
        assert isinstance(request, TingwuRequest)
        assert request.TaskKey == "test_task_123"

    def test_validate_chat_request_function(self):
        """测试聊天请求验证函数"""
        data = {
            "SysContent": "你是一个AI助手",
            "UserContent": "你好"
        }
        request = validate_chat_request(data)
        assert isinstance(request, ChatRequest)
        assert request.SysContent == "你是一个AI助手"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
