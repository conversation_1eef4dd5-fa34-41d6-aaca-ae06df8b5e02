from docx import Document

def print_styles(doc_path):
    try:
        # 打开 Word 文档
        doc = Document(doc_path)
        # 遍历文档中的所有样式
        for style in doc.styles:
            # 根据样式类型赋值描述
            if style.type == 1:
                style_type = "段落样式 (Paragraph)"
            elif style.type == 2:
                style_type = "字符样式 (Character)"
            elif style.type == 3:
                style_type = "表格样式 (Table)"
            else:
                style_type = "未知样式 (Unknown)"
            # 打印样式名称和类型
            print(f"样式名称: {style.name}, 类型: {style_type}")
    except FileNotFoundError:
        print(f"错误：文件 {doc_path} 不存在。")
    except Exception as e:
        print(f"发生错误：{e}")

# 示例调用
print_styles('template.docx')