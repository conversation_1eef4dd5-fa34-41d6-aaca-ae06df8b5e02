# -*- coding: utf-8 -*-
import os
import time
import json
import requests
import markdown
import webbrowser
from typing import Optional, Tuple


"""
对话式智能体开发范式:
  1. 在网页界面生成token;
  2. 在网页界面创建智能体, 获取智能体的bot_id;
  3. 发起一个对话, 得到chat_id和conversation_id;
  4. 客户端轮询对话任务状态, 检测是否已完成;
  5. 如果已完成, 则获取对话结果.

创建智能体:
https://www.coze.cn/space/develop

调试智能体:
https://www.coze.cn/open/playground
"""

# 配置常量
TOKEN = 'pat_vUjvTqRlhhFnFxkwGTAuUqKYDUXahiIZRXQLhouBigqZ72710e7syxpe4fByj4lg'
BOT_ID = "7559014733291143203"  # 智能体ID, 本智能体用于Python教学.
CHAT_URL = "https://api.coze.cn/v3/chat"
RETRIEVE_URL = "https://api.coze.cn/v3/chat/retrieve"
MESSAGE_LIST_URL = "https://api.coze.cn/v3/chat/message/list"

# HTTP标头
HEADERS = {
            'Authorization': f'Bearer {TOKEN}',
            'Content-Type': 'application/json'
        }

def send_chat_request(bot_id: str, content: any, user_id: str, content_type: str = "text") -> Optional[Tuple[str, str]]:
    """
    发送聊天请求并返回chat_id和conversation_id.

    Args:
        bot_id: 智能体ID
        content: 消息内容
        user_id: 用户ID
        content_type: 内容类型, 默认为"text", 可选"object_string"

    Returns:
        Optional[Tuple[str, str]]: 成功时返回(chat_id, conversation_id)元组, 失败时返回None.

    """
    # 构建消息内容
    if content_type == "object_string":
        content = json.dumps(content)

    # 构建请求负载
    payload = {
        "bot_id": bot_id,
        "user_id": user_id,
        "stream": False,
        "auto_save_history": True,
        "additional_messages": [{
            "role": "user",
            "content": content,
            "content_type": content_type
        }]}

    try:
        # 发送HTTP消息
        response = requests.post(
            CHAT_URL,
            headers = HEADERS,
            data = json.dumps(payload)
        )

        # HTTP状态码检查
        if response.status_code != 200:
            print(f"Chat请求失败: {response.status_code} - {response.text}")
            return None
        # 解析HTTP响应
        result = response.json()
        # API返回码检查
        if result.get('code') != 0:
            print(f"API错误: {result.get('msg')}")
            return None

        data = result['data']
        print(f"Chat请求成功, chat_id: {data['id']}, conversation_id: {data['conversation_id']}")
        return data['id'], data['conversation_id']

    except Exception as e:
        print(f"发送聊天请求出错: {str(e)}")
        return None


def get_processed_content(chat_id: str, conversation_id: str) -> Optional[str]:
    """
    获取智能体结果.

    Args:
        chat_id: 聊天ID
        conversation_id: 对话ID

    Returns:
        Optional[str]: 智能体结果, 如果未找到则返回None.
    """
    print(f"开始获取处理结果...")

    # 构建请求URL
    url = f"{MESSAGE_LIST_URL}?chat_id={chat_id}&conversation_id={conversation_id}"

    try:
        response = requests.get(url, headers = HEADERS)

        # HTTP状态码检查
        if response.status_code != 200:
            print(f"获取消息失败: {response.status_code} - {response.text}")
            return None

        # 解析HTTP响应
        result = response.json()
        code = result.get('code')

        # API返回码检查
        if code != None and code != 0:
            print(f"API错误: {result.get('msg')}")
            return None

        # 返回消息列表
        messages = result.get('data', [])
        return messages

    except Exception as e:
        print(f"获取消息出错: {str(e)}")
        return None


def retrieve_chat(chat_id: str, conversation_id: str, max_retries: int = 24, retry_interval: int = 5) -> Optional[str]:
    """
    通过客户端轮询的方式检索对话的运行状态, 如果已完成, 则获取对话结果.

    Args:
        chat_id: 聊天ID
        conversation_id: 对话ID
        max_retries: 最大重试次数, 默认24次
        retry_interval: 重试间隔时间, 默认5秒

    Returns:
        Optional[str]: 对话结果, 如果未找到则返回None.
    """
    print(f"开始检索运行状态...")

    # 构建请求URL
    url = f"{RETRIEVE_URL}?chat_id={chat_id}&conversation_id={conversation_id}"

    for attempt in range(max_retries):
        print(f"\n尝试 {attempt + 1}/{max_retries}...")
        try:
            response = requests.get(url, headers = HEADERS)

            # HTTP状态码检查
            if response.status_code != 200:
                print(f"获取消息失败: {response.status_code} - {response.text}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                continue

            # 解析HTTP响应
            result = response.json()
            code = result.get('code')
            print(f"In Attempt {attempt}, API returns: {result}")

            # API返回码检查
            if code != None and code != 0:
                print(f"API错误: {result.get('msg')}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_interval} 秒后重试...")
                    time.sleep(retry_interval)
                continue

            # 返回消息列表
            data = result.get('data', {})
            status = data.get('status')
            if status == 'completed':
                print(f"\n对话已完成, 获取处理结果...\n")
                return get_processed_content(chat_id, conversation_id)

            print(f"智能体运行状态: {status}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
        except Exception as e:
            print(f"获取消息出错: {str(e)}")
            return None


def markdown_to_html(markdown_text: str, output_filepath: str = 'output.html') -> bool:
    """
    将Markdown文本转换为HTML并保存到指定文件, 并用浏览器打开.

    Args:
        markdown_text (str): 包含Markdown格式的字符串.
        output_filepath (str): 要保存HTML内容的文件路径(例如 'output.html').

    Returns:
        bool: 如果转换和保存成功, 则返回True; 否则返回False.
    """
    try:
        # 将Markdown转换为HTML
        html_content = markdown.markdown(markdown_text, extensions=['fenced_code', 'tables'])

        # 构造完整的HTML页面结构
        full_html_page = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coze智能体运行结果</title>
</head>
<body>
{html_content}
</body>
</html>
"""
        # 将HTML内容写入文件
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write(full_html_page)
            print(f"成功将Markdown转换为HTML并保存到: {output_filepath}")

        # 获取文件的绝对路径, 确保浏览器能找到它
        full_path = os.path.abspath(output_filepath)
        # 使用 webbrowser 模块打开文件
        webbrowser.open(f'file://{full_path}')

        return True
    except Exception as e:
        print(f"保存文件时发生错误: {e}")
        return False


def main():
    """
    用户输入提示词, 发送聊天请求到智能体, 等待智能体运行, 获取智能体的处理结果, 把默认的markdown结果转换成HTML, 保存到HTML文件并打开.
    """
    # 用户输入文件夹路径
    prompt = input("请输入提示词(例: 生成一道关于Python递归的算法题, 给出示例代码并解释其思路): ").strip()

    user_id = f"USER_{int(time.time())}"

    # 发送聊天请求到智能体
    chat_info = send_chat_request(BOT_ID, prompt, user_id, "text")
    if not chat_info:
        print("智能体Chat请求失败, 程序终止")
        return False
    else:
        print(f"成功发送Chat请求(chat_id, conversation_id): {chat_info}")

    chat_id, conversation_id = chat_info # 解构chat信息

    # 等待智能体运行
    print("查询智能体运行结果...")

    # 获取智能体的处理结果
    content = retrieve_chat(chat_id, conversation_id, 10, 5)
    if not content:
        print("未能获取智能体的处理结果")
        return False
    else:
        print(content)
        # 获取markdown文本
        markdown_text = content[0]['content']
        # 把默认的markdown结果转换成HTML, 保存到HTML文件并打开
        markdown_to_html(markdown_text)
        return True


if __name__ == "__main__":
    main()
