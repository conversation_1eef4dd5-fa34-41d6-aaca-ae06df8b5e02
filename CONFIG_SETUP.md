# AIHUB 配置文件设置指南

## 概述

AIHUB 使用三个主要配置文件：

1. **服务器配置**: `./config/config.ini` - 服务器运行参数（端口、性能等）
2. **阿里云凭证**: `~/.alibabacloud/credentials.ini` - 阿里云API凭证
3. **聊天API密钥**: `~/.alibabacloud/chat.ini` - 通义千问API密钥

## 快速设置（Linux/macOS）

```bash
# 1. 创建阿里云配置目录
mkdir -p ~/.alibabacloud

# 2. 复制示例配置文件
cp ./config/credentials.ini ~/.alibabacloud/
cp ./config/chat.ini ~/.alibabacloud/

# 3. 编辑配置文件，填入真实的API密钥
vim ~/.alibabacloud/credentials.ini
vim ~/.alibabacloud/chat.ini
```

## 快速设置（Windows）

```powershell
# 1. 创建阿里云配置目录
mkdir $env:USERPROFILE\.alibabacloud

# 2. 复制示例配置文件
Copy-Item .\config\credentials.ini $env:USERPROFILE\.alibabacloud\
Copy-Item .\config\chat.ini $env:USERPROFILE\.alibabacloud\

# 3. 编辑配置文件，填入真实的API密钥
notepad $env:USERPROFILE\.alibabacloud\credentials.ini
notepad $env:USERPROFILE\.alibabacloud\chat.ini
```

## 配置文件详解

### 1. 服务器配置 (`./config/config.ini`)

```ini
[default]
# 服务器配置
host = 0.0.0.0
port = 5579
debug = false
workers = 4
timeout = 60

# API配置
endpoint = tingwu.cn-beijing.aliyuncs.com
api_version = 2023-09-30

# 性能配置
connection_pool_size = 10
connect_timeout = 30000
read_timeout = 60000
max_retries = 3

[development]
# 开发环境配置
debug = true
port = 5579

[production]
# 生产环境配置
debug = false
port = 80
workers = 8
```

### 2. 阿里云凭证 (`~/.alibabacloud/credentials.ini`)

```ini
[default]
type = access_key
access_key_id = YOUR_ACCESS_KEY_ID
access_key_secret = YOUR_ACCESS_KEY_SECRET
region_id = cn-beijing
app_key = YOUR_TINGWU_APP_KEY

[development]
type = access_key
access_key_id = YOUR_DEV_ACCESS_KEY_ID
access_key_secret = YOUR_DEV_ACCESS_KEY_SECRET
region_id = cn-beijing
app_key = YOUR_DEV_TINGWU_APP_KEY

[production]
type = access_key
access_key_id = YOUR_PROD_ACCESS_KEY_ID
access_key_secret = YOUR_PROD_ACCESS_KEY_SECRET
region_id = cn-beijing
app_key = YOUR_PROD_TINGWU_APP_KEY
```

### 3. 聊天API密钥 (`~/.alibabacloud/chat.ini`)

```ini
[default]
type = access_key
app_key = sk-YOUR_DASHSCOPE_API_KEY

[development]
type = access_key
app_key = sk-YOUR_DEV_DASHSCOPE_API_KEY

[production]
type = access_key
app_key = sk-YOUR_PROD_DASHSCOPE_API_KEY
```

## 获取API密钥

### 阿里云凭证

1. 登录 [阿里云控制台](https://ram.console.aliyun.com/manage/ak)
2. 创建AccessKey
3. 记录 `access_key_id` 和 `access_key_secret`

### 通义听悟 AppKey

1. 登录 [通义听悟控制台](https://tingwu.aliyun.com/)
2. 创建应用获取 AppKey

### 通义千问 API Key

1. 登录 [DashScope控制台](https://dashscope.console.aliyun.com/)
2. 创建API Key

## 环境设置

### 环境切换（重要）

**环境配置通过 `AIHUB_ENVIRONMENT` 环境变量控制，而不是配置文件中的设置。**

#### Windows 环境设置：

```powershell
# 设置为开发环境
$env:AIHUB_ENVIRONMENT="development"
python -m app.main

# 设置为生产环境
$env:AIHUB_ENVIRONMENT="production"
python -m app.main

# 临时设置并启动（推荐）
$env:AIHUB_ENVIRONMENT="development"; python -m app.main
```

#### Linux/macOS 环境设置：

```bash
# 设置为开发环境
export AIHUB_ENVIRONMENT=development
python -m app.main

# 设置为生产环境
export AIHUB_ENVIRONMENT=production
python -m app.main

# 临时设置并启动（推荐）
AIHUB_ENVIRONMENT=development python -m app.main
```

### 其他环境变量配置（可选）

如果不想使用配置文件，也可以通过环境变量配置：

```bash
# 阿里云凭证
export ALIBABA_CLOUD_ACCESS_KEY_ID="your_access_key_id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your_access_key_secret"
export ALIBABA_CLOUD_REGION_ID="cn-beijing"

# 服务器配置
export AIHUB_HOST="0.0.0.0"
export AIHUB_PORT="5579"
export AIHUB_APP_KEY="your_app_key"
```

## 配置优先级

配置读取优先级（从高到低）：

1. **环境变量** - 最高优先级
2. **配置文件** - 中等优先级
   - `~/.alibabacloud/credentials.ini` (阿里云凭证)
   - `~/.alibabacloud/chat.ini` (聊天API)
   - `./config/config.ini` (服务器配置)
3. **默认值** - 最低优先级

## 测试配置

运行测试脚本验证配置是否正确：

```bash
python test_config.py
```

## 启动服务

### Windows 启动：

```powershell
# 开发模式
$env:AIHUB_ENVIRONMENT="development"; python -m app.main

# 生产模式
$env:AIHUB_ENVIRONMENT="production"; python -m app.main

# 默认模式（使用default配置）
python -m app.main
```

### Linux/macOS 启动：

```bash
# 开发模式
AIHUB_ENVIRONMENT=development python -m app.main

# 生产模式
AIHUB_ENVIRONMENT=production python -m app.main

# 默认模式（使用default配置）
python -m app.main
```

## 故障排除

### 常见错误

1. **AppKey未配置**
   - 检查 `~/.alibabacloud/credentials.ini` 中的 `app_key` 配置
   - 或设置环境变量 `TINGWU_APP_KEY` 或 `ALIBABA_CLOUD_APP_KEY`

2. **阿里云凭证缺失**
   - 确保 `~/.alibabacloud/credentials.ini` 文件存在
   - 检查文件中的 `access_key_id` 和 `access_key_secret` 配置

3. **聊天API密钥缺失**
   - 确保 `~/.alibabacloud/chat.ini` 文件存在
   - 检查文件中的 `app_key` 配置

### 调试模式

启用调试模式获取更多日志信息：

```bash
# 设置环境变量
export AIHUB_DEBUG=true

# 或修改 config.ini
[default]
debug = true
```

## 安全建议

1. **不要将真实的API密钥提交到版本控制系统**
2. **使用不同的密钥区分开发和生产环境**
3. **定期轮换API密钥**
4. **限制API密钥的访问权限**
5. **监控API密钥的使用情况**

## 支持

如果遇到配置问题，请：

1. 运行 `python test_config.py` 检查配置状态
2. 查看应用日志文件 `./logs/aihub.log`
3. 检查配置文件格式是否正确
4. 确认API密钥是否有效