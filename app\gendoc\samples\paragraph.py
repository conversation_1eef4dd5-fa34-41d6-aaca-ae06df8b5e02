from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn

# 创建一个新的 Word 文档
doc = Document()

# 添加一个段落
paragraph = doc.add_paragraph()

# 添加一个运行（包含文本）
run = paragraph.add_run("这是一个示例段落。")

# 设置字体和字号
run.font.name = "Arial"  # 设置字体为 Arial
run.font.size = Pt(14)   # 设置字号为 14 磅

# 设置段落对齐方式为居中
paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

doc.add_paragraph()

doc.add_page_break()

paragraph2 = doc.add_paragraph()
run1 = paragraph2.add_run("第一部分 ")
run1.font.name = "Arial"
run1.font.size = Pt(12)

run2 = paragraph2.add_run("第二部分")
run2.font.name = "SimSun"
run2.font.size = Pt(16)
# 设置东亚字体以确保中文字符正确显示
run2._element.rPr.rFonts.set(qn('w:eastAsia'), "SimSun")
paragraph2.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

# 保存文档
doc.save("paragraph.docx")
