# -*- coding: utf-8 -*-
"""
Gunicorn配置文件 - 高性能生产环境配置

- Workers数量：CPU核心数 * 2 + 1
- Worker类型：uvicorn.workers.UvicornWorker
- Worker连接数：1000
- 超时：120秒
- Keep-alive：5秒
- 最大请求数：2000
- 使用预加载和内存文件系统优化
"""

import multiprocessing
import os

# 服务器绑定配置
bind = "127.0.0.1:5579"

# backlog 参数直接对应于TCP/IP协议栈中 listen() 系统调用的 backlog 参数。它指的是TCP连接的“等待队列”或“积压队列”的大小。
# Gunicorn的默认backlog值是 2048。这是一个非常大且通常足够安全的值，是为高流量网站设计的。
backlog = 128

# Worker进程配置
workers = multiprocessing.cpu_count() # 对于I/O密集型应用: max(4, CPU核心数 * 2 + 1)
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 500 # 用于限制异步工作进程（如 gevent 或 eventlet）可以同时处理的客户端连接数。

# 每个工作进程中处理请求的线程数。
# 对于CPU密集型任务，由于Python的GIL（全局解释器锁）存在，
# 多线程并不能实现真正的并行计算。增加线程数反而可能因为线程切换
# 带来性能开销。因此保持默认的1即可。
threads = 1  # 默认1，通常无需设置

# 超时配置
# Gunicorn的默认超时时间是30秒。你的请求耗时一分钟，所以必须调高它。
# 如果一个worker处理一个请求的时间超过这个值，Gunicorn会认为它卡死了并将其杀死重启。
timeout = 300
# 客户端连接在超过这个秒数后没有新活动，则被认为是超时的。
# 对于长轮询等场景可能需要调整，但对于AIhub的场景，默认值通常足够。
keepalive = 5
# 在重启一个平滑重启(graceful restart)期间，Gunicorn会给旧的worker一段
# 时间来完成当前正在处理的请求。这个值通常也需要设置得比请求最大耗时要长。
graceful_timeout = 300

# 自动重载配置
reload = False # 自动重载，当代码有变化时，自动重启worker进程
reload_extra_files = ["app/"] # 监听的文件列表，当这些文件有变化时，自动重启worker进程
reload_delay = 1 # 重启延迟，单位秒，默认是0秒，即立即重启。
reload_engine = "auto" # 重启引擎，默认是auto，即自动选择重启引擎。
reload_timeout = 300 # 重启超时，单位秒，默认是30秒。

# 这两个参数用于防止内存泄漏
max_requests = 200  # 工作进程在处理完指定数量的请求后自动重启。
max_requests_jitter = 50  # 为这个重启阈值增加一个随机数，以防止所有工作进程在同一时间重启，导致服务瞬间不可用。这个配置让worker在处理1000到1050个请求之间的某个随机点重启。

# 这个参数决定了应用代码的加载时机
# True: Gunicorn会在启动工作进程之前（在主进程中）预加载应用。所有工作进程都会从主进程fork（派生）出来，共享一部分初始内存。
# False: 默认值, 推荐, 每个工作进程在自己启动时独立加载应用代码。
# 预加载（True）会导致数据库连接、文件句柄等资源在主进程中被初始化，然后被所有子进程共享，这通常会引发各种难以调试的并发问题。对于你使用应用工厂create_app的模式，更应该在每个进程中独立创建应用实例和相关资源。
# 设置为False可以让你在不中断服务的情况下更新代码。当你向Gunicorn主进程发送HUP信号时，它会逐步用加载了新代码的worker替换掉旧的worker。如果preload_app为True，你必须完全停止并重启整个服务才能让新代码生效。
preload_app = False

# 内存优化
# 使用内存文件系统作为临时目录（Linux生产环境）
if os.path.exists('/dev/shm'):
    worker_tmp_dir = '/dev/shm'
else:
    worker_tmp_dir = None

# 日志配置
# accesslog输出到stdout，这样会通过start.sh重定向到aihub.log
accesslog = "-"
# errorlog不再输出到stderr，而是输出到专门的gunicorn错误日志文件
# 这样可以避免INFO级别的日志被重定向到aihub_error.log
errorlog = "./logs/aihub_gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
pidfile = "./logs/gunicorn.pid"
user = "hxz" # 指定Gunicorn工作进程运行的用户身份, 推荐www-data。
group = "users" # 指定Gunicorn工作进程运行的用户组身份, 推荐www-data。
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094 # 限制HTTP请求行的最大长度
limit_request_fields = 100 # 限制HTTP请求头的最大数量
limit_request_field_size = 8190 # 限制HTTP请求头每个字段的最大长度

proc_name = "lcmp_aihub_app" # 设置Gunicorn进程在操作系统中的显示名称。

# SSL配置（如果需要）
# keyfile = None
# certfile = None

# 开发环境配置覆盖
if os.getenv('AIHUB_ENVIRONMENT') == 'development':
    # 开发环境使用较少的worker和更详细的日志
    loglevel = "debug"
    reload = True

# 生产环境特殊配置
if os.getenv('AIHUB_ENVIRONMENT') == 'production':
    # 生产环境优化配置
    loglevel = "info"
    reload = False

# 从环境变量读取配置覆盖
if os.getenv('GUNICORN_WORKERS'):
    workers = int(os.getenv('GUNICORN_WORKERS'))

if os.getenv('GUNICORN_TIMEOUT'):
    timeout = int(os.getenv('GUNICORN_TIMEOUT'))

if os.getenv('GUNICORN_BIND'):
    bind = os.getenv('GUNICORN_BIND')

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("AI Hub服务器正在启动...")
    server.log.info(f"Workers: {workers}")
    server.log.info(f"Worker类型: {worker_class}")
    server.log.info(f"绑定地址: {bind}")
    server.log.info(f"超时时间: {timeout}秒")

def on_reload(server):
    """重新加载时调用"""
    server.log.info("AI Hub服务器正在重新加载...")

def worker_int(worker):
    """Worker收到SIGINT信号时调用"""
    worker.log.info(f"Worker {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """Worker fork之前调用"""
    server.log.info(f"Worker {worker.pid} 即将启动")

def post_fork(server, worker):
    """Worker fork之后调用"""
    server.log.info(f"Worker {worker.pid} 已启动")

def worker_abort(worker):
    """Worker异常退出时调用"""
    worker.log.error(f"Worker {worker.pid} 异常退出")

def post_request(worker, req, environ, resp):
    """请求处理完成后调用"""
    # 记录请求处理时间和资源使用
    server.log.info(f"请求 {req.path} 处理完成，耗时 {worker.request_time} 秒，占用内存 {worker.memory_usage()} MB")
