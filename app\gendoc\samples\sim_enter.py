from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_LINE_SPACING

# 1. 创建一个新的Word文档对象
doc = Document()

# 添加第一段文字
doc.add_paragraph("这是第一段文字。")

# --- 完美创建“有字号的”高空白行的核心代码 ---

# 2. 定义我们想要的高度和字号（让它们保持一致）
TARGET_SIZE = 48

# 3. 添加一个新段落
blank_paragraph = doc.add_paragraph()

# 4. 获取该段落的格式对象
p_format = blank_paragraph.paragraph_format

# 5. 【控制物理高度】将行距规则设置为“固定值”，并指定其高度
#    这是强制锁定高度的关键，确保视觉效果正确无误。
p_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
p_format.line_spacing = Pt(TARGET_SIZE)

# 6. 【控制UI显示】在该段落中添加一个带零宽空格的Run
#    使用零宽空格 '\u200B' 作为“挂载”字体格式的实体。
run = blank_paragraph.add_run('\u200B')

# 7. 【控制UI显示】为这个Run设置字体大小
#    这确保当光标点击该行时，Word界面会显示正确的字号。
run.font.size = Pt(TARGET_SIZE)

# --- 核心代码结束 ---

# 添加第三段文字，以验证效果
doc.add_paragraph(f"这是第三段文字。上面是一个高度和字号都为 {TARGET_SIZE} 磅的空白行。")


# 8. 保存文档
doc.save("sim_enter.docx")

print("文档 '最终版_高度和字号都设置的空白行.docx' 已成功创建。")
