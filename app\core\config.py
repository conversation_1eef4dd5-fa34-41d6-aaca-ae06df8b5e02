#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千义听悟音频转写服务器 - 配置管理模块
适用于Linux生产环境的凭证配置管理

支持多种配置来源：
1. 环境变量（优先级最高）
2. 配置文件
3. 默认值（优先级最低）

配置文件路径：
1. 环境变量 TINGWU_CONFIG_FILE 指定的路径
2. ./config/credentials.conf
"""

import os
import configparser
from typing import Dict, Any, Optional
from pathlib import Path

from .logger import get_logger

logger = get_logger(__name__)

class ConfigManager:
    """配置管理器"""

    def __init__(self, environment: str = 'default'):
        """
        初始化配置管理器

        Args:
            environment (str): 环境名称 (default/development/production)
        """
        self.environment = environment
        self.config = configparser.ConfigParser()
        self._config_loaded = False
        self._config_file_path = None

        # 默认配置
        self.defaults = {
            # 阿里云凭证配置
            'access_key_id': '',
            'access_key_secret': '',
            'region_id': 'cn-beijing',

            # 服务器配置
            'host': '0.0.0.0',
            'port': 5579,
            'debug': False,
            'workers': 4,
            'timeout': 60,

            # 性能配置
            'threads': 4,
            'connection_limit': 100,
            'cleanup_interval': 30,
            'channel_timeout': 120,

            # 日志配置
            'log_dir': 'logs',
            'log_level': 20,
            'log_level_name': 'INFO',
            'backup_count': 30,

            # API配置
            'endpoint': 'tingwu.cn-beijing.aliyuncs.com',
            'api_version': '2021-12-21',
            'app_key': '',  # 千义听悟项目的AppKey
            'ssl_verify': True,
            'connect_timeout': 30,
            'read_timeout': 60,
            'max_retries': 3,
            'retry_delay': 1,

            # 日志记录配置
            'log_api_calls': True,
            'log_request_data': False,
            'log_response_data': False,
            'mask_sensitive_data': True,

            # 静态文件配置
            'static_dir': 'static',

            # 连接池配置
            'connection_pool_size': 10,
            'connection_reuse': True,
            'request_queue_size': 100
        }

        logger.info(f"配置管理器初始化，环境: {environment}")
        self._load_config()

    def _get_config_file_paths(self) -> list:
        """获取配置文件路径"""
        paths = []

        # 1. 环境变量指定的路径（最高优先级）
        env_config_file = os.getenv('AIHUB_CONFIG_FILE')
        if env_config_file:
            paths.append(Path(env_config_file))

        # 2. 项目级主配置文件（高优先级）
        main_config = Path('./config/config.ini')
        paths.append(main_config)

        # 3. 用户目录下的阿里云凭证文件
        home_dir = Path.home()
        # 3.1 ~/.alibabacloud/credentials.ini（SDK支持的路径）
        alibabacloud_config = home_dir / '.alibabacloud' / 'credentials.ini'
        paths.append(alibabacloud_config)

        # 设置环境变量，告诉SDK使用.ini文件
        os.environ['ALIBABA_CLOUD_CREDENTIALS_FILE'] = str(alibabacloud_config)

        # 3.2 ~/.aliyun/config.json（SDK默认查找路径）
        aliyun_config = home_dir / '.aliyun' / 'config.json'
        paths.append(aliyun_config)

        # 3.3 ~/.alibabacloud/credentials.ini（SDK默认查找路径）
        alibabacloud_ini = home_dir / '.alibabacloud' / 'credentials.ini'
        paths.append(alibabacloud_ini)

        # 4. 项目级凭证配置文件（最低优先级）
        project_config = Path('./config/credentials.conf')
        paths.append(project_config)

        return paths

    def _load_config(self):
        """加载配置文件"""
        config_paths = self._get_config_file_paths()

        for config_path in config_paths:
            if config_path.exists() and config_path.is_file():
                try:
                    self.config.read(config_path, encoding='utf-8')
                    self._config_file_path = str(config_path)
                    self._config_loaded = True
                    logger.info(f"成功加载配置文件: {config_path}")

                    # 检查文件权限（仅在Linux/Unix系统）
                    if os.name == 'posix':
                        file_stat = config_path.stat()
                        file_mode = oct(file_stat.st_mode)[-3:]
                        if file_mode != '600':
                            logger.warning(f"配置文件权限不安全: {file_mode}，建议设置为600")

                    break
                except Exception as e:
                    logger.warning(f"加载配置文件失败 {config_path}: {e}")
                    continue

        if not self._config_loaded:
            logger.warning("未找到配置文件，将使用环境变量和默认值")

    def get(self, key: str, section: str = None) -> Any:
        """
        获取配置值

        优先级：环境变量 > 配置文件 > 默认值

        Args:
            key (str): 配置键名
            section (str): 配置节名，默认使用当前环境

        Returns:
            Any: 配置值
        """
        if section is None:
            section = self.environment

        # 1. 优先从环境变量获取
        env_key = f'ALIBABA_CLOUD_{key.upper()}' if key in ['access_key_id', 'access_key_secret', 'region_id'] else f'AIHUB_{key.upper()}'
        env_value = os.getenv(env_key)
        if env_value is not None:
            return self._convert_value(env_value)

        # 2. 从配置文件获取
        if self._config_loaded:
            try:
                if self.config.has_section(section) and self.config.has_option(section, key):
                    value = self.config.get(section, key)
                    return self._convert_value(value)
                # 如果指定节不存在，尝试从default节获取
                elif section != 'default' and self.config.has_section('default') and self.config.has_option('default', key):
                    value = self.config.get('default', key)
                    return self._convert_value(value)
            except Exception as e:
                logger.warning(f"读取配置文件值失败 [{section}]{key}: {e}")

        # 3. 返回默认值
        return self.defaults.get(key)

    def _convert_value(self, value: str) -> Any:
        """转换配置值类型"""
        if isinstance(value, str):
            # 布尔值转换
            if value.lower() in ('true', 'yes', '1', 'on'):
                return True
            elif value.lower() in ('false', 'no', '0', 'off'):
                return False

            # 数字转换
            try:
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            except ValueError:
                pass

        return value

    def get_aliyun_credentials(self) -> Dict[str, str]:
        """获取阿里云凭证配置"""
        # 首先尝试从环境变量获取（最高优先级）
        access_key_id = os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID') or os.getenv('ALIYUN_ACCESS_KEY_ID')
        access_key_secret = os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET') or os.getenv('ALIYUN_ACCESS_KEY_SECRET')
        region_id = os.getenv('ALIBABA_CLOUD_REGION_ID') or os.getenv('ALIYUN_REGION_ID') or 'cn-beijing'

        # 如果环境变量中没有，尝试从阿里云凭证文件获取
        if not (access_key_id and access_key_secret):
            # 阿里云凭证文件路径
            credentials_paths = [
                os.path.expanduser('~/.alibabacloud/credentials.ini'),
                './config/credentials.ini'
            ]

            for cred_path in credentials_paths:
                if os.path.exists(cred_path):
                    logger.info(f"从阿里云凭证文件获取凭证: {cred_path}")

                    # 创建临时配置解析器
                    cred_config = configparser.ConfigParser()
                    try:
                        cred_config.read(cred_path, encoding='utf-8')
                        sections = cred_config.sections()
                        logger.debug(f"凭证文件中的节: {sections}")

                        # 按优先级尝试不同的节
                        # 1. 当前环境节
                        if self.environment in sections:
                            logger.debug(f"尝试从 [{self.environment}] 节获取凭证")
                            access_key_id = access_key_id or cred_config.get(self.environment, 'access_key_id', fallback='')
                            access_key_secret = access_key_secret or cred_config.get(self.environment, 'access_key_secret', fallback='')
                            region_id = region_id or cred_config.get(self.environment, 'region_id', fallback='cn-beijing')

                        # 2. default节
                        if not (access_key_id and access_key_secret) and 'default' in sections:
                            logger.debug("尝试从 [default] 节获取凭证")
                            access_key_id = access_key_id or cred_config.get('default', 'access_key_id', fallback='')
                            access_key_secret = access_key_secret or cred_config.get('default', 'access_key_secret', fallback='')
                            region_id = region_id or cred_config.get('default', 'region_id', fallback='cn-beijing')

                        # 3. 任何其他可用的节
                        if not (access_key_id and access_key_secret) and sections:
                            for section in sections:
                                if section not in [self.environment, 'default']:
                                    logger.debug(f"尝试从 [{section}] 节获取凭证")
                                    access_key_id = access_key_id or cred_config.get(section, 'access_key_id', fallback='')
                                    access_key_secret = access_key_secret or cred_config.get(section, 'access_key_secret', fallback='')
                                    region_id = region_id or cred_config.get(section, 'region_id', fallback='cn-beijing')

                                    if access_key_id and access_key_secret:
                                        logger.info(f"从 [{section}] 节成功获取凭证")
                                        break

                        # 如果找到凭证就退出循环
                        if access_key_id and access_key_secret:
                            logger.info(f"成功从凭证文件获取阿里云凭证: {cred_path}")
                            break

                    except Exception as e:
                        logger.warning(f"读取凭证文件失败 {cred_path}: {e}")
                        continue
                else:
                    logger.debug(f"凭证文件不存在: {cred_path}")

        credentials = {
            'access_key_id': access_key_id or '',
            'access_key_secret': access_key_secret or '',
            'region_id': region_id or 'cn-beijing'
        }

        # 验证必需的凭证
        missing_keys = [k for k in ['access_key_id', 'access_key_secret'] if not credentials[k]]
        if missing_keys:
            logger.error(f"缺少必需的阿里云凭证: {missing_keys}")
            raise ValueError(
                f"缺少必需的阿里云凭证: {missing_keys}，请检查以下来源:\n"
                f"1. 环境变量: ALIBABA_CLOUD_ACCESS_KEY_ID, ALIBABA_CLOUD_ACCESS_KEY_SECRET\n"
                f"2. 配置文件: {self._config_file_path or '~/.alibabacloud/credentials.ini'}"
            )

        # 脱敏日志
        masked_credentials = {
            'access_key_id': credentials['access_key_id'][:4] + '***' if credentials['access_key_id'] and len(credentials['access_key_id']) > 4 else '***',
            'access_key_secret': '***',
            'region_id': credentials['region_id']
        }
        logger.info(f"阿里云凭证配置: {masked_credentials}")

        return credentials

    def get_app_key(self) -> str:
        """获取通义听悟AppKey配置

        优先级：环境变量 > credentials.ini文件 > 默认值
        """
        # 首先尝试从环境变量获取（最高优先级）
        app_key = os.getenv('TINGWU_APP_KEY') or os.getenv('ALIBABA_CLOUD_APP_KEY')

        # 如果环境变量中没有，尝试从阿里云凭证文件获取
        if not app_key:
            # 阿里云凭证文件路径
            credentials_paths = [
                os.path.expanduser('~/.alibabacloud/credentials.ini'),
                './config/credentials.ini'
            ]

            for cred_path in credentials_paths:
                if os.path.exists(cred_path):
                    logger.info(f"从阿里云凭证文件获取AppKey: {cred_path}")

                    # 创建临时配置解析器
                    cred_config = configparser.ConfigParser()
                    try:
                        cred_config.read(cred_path, encoding='utf-8')
                        sections = cred_config.sections()
                        logger.debug(f"凭证文件中的节: {sections}")

                        # 按优先级尝试不同的节
                        # 1. 当前环境节
                        if self.environment in sections:
                            logger.debug(f"尝试从 [{self.environment}] 节获取AppKey")
                            app_key = app_key or cred_config.get(self.environment, 'app_key', fallback='')

                        # 2. default节
                        if not app_key and 'default' in sections:
                            logger.debug("尝试从 [default] 节获取AppKey")
                            app_key = app_key or cred_config.get('default', 'app_key', fallback='')

                        # 3. 任何其他可用的节
                        if not app_key and sections:
                            for section in sections:
                                if section not in [self.environment, 'default']:
                                    logger.debug(f"尝试从 [{section}] 节获取AppKey")
                                    app_key = app_key or cred_config.get(section, 'app_key', fallback='')

                                    if app_key:
                                        logger.info(f"从 [{section}] 节成功获取AppKey")
                                        break

                        # 如果找到AppKey就退出循环
                        if app_key:
                            logger.info(f"成功从凭证文件获取AppKey: {cred_path}")
                            break

                    except Exception as e:
                        logger.warning(f"读取凭证文件失败 {cred_path}: {e}")
                        continue
                else:
                    logger.debug(f"凭证文件不存在: {cred_path}")

        # 验证AppKey
        if not app_key:
            logger.error("缺少必需的通义听悟AppKey")
            raise ValueError(
                "缺少必需的通义听悟AppKey，请检查以下来源:\n"
                "1. 环境变量: TINGWU_APP_KEY 或 ALIBABA_CLOUD_APP_KEY\n"
                "2. 配置文件: ~/.alibabacloud/credentials.ini 或 ./config/credentials.ini\n"
                "3. 在配置文件的相应节中添加 app_key = your_app_key"
            )

        # 脱敏日志
        masked_app_key = app_key[:8] + '***' if len(app_key) > 8 else '***'
        logger.info(f"通义听悟AppKey配置: {masked_app_key}")

        return app_key

    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        config = {
            'endpoint': self.get('endpoint'),
            'api_version': self.get('api_version'),
            'ssl_verify': self.get('ssl_verify'),
            'connect_timeout': self.get('connect_timeout'),
            'read_timeout': self.get('read_timeout'),
            'max_retries': self.get('max_retries'),
            'retry_delay': self.get('retry_delay')
        }

        logger.info(f"API配置: {config}")
        return config

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        config = {
            'log_api_calls': self.get('log_api_calls'),
            'log_request_data': self.get('log_request_data'),
            'log_response_data': self.get('log_response_data'),
            'mask_sensitive_data': self.get('mask_sensitive_data')
        }

        logger.info(f"日志配置: {config}")
        return config

    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        config = {
            'connection_pool_size': self.get('connection_pool_size'),
            'connection_reuse': self.get('connection_reuse'),
            'request_queue_size': self.get('request_queue_size')
        }

        logger.info(f"性能配置: {config}")
        return config

    def get_retry_config(self) -> Dict[str, Any]:
        """获取重试配置"""
        config = {
            'enabled': True,
            'max_attempts': self.get('max_retries'),
            'backoff_multiplier': self.get('retry_delay')
        }

        logger.info(f"重试配置: {config}")
        return config

    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        config = {
            'host': self.get('host') or '0.0.0.0',
            'port': self.get('port') or 5579,
            'debug': self.get('debug') or False,
            'workers': self.get('workers') or 4,
            'timeout': self.get('timeout') or 60
        }

        logger.info(f"服务器配置: {config}")
        return config

    def get_static_dir(self) -> str:
        """获取静态文件目录配置

        优先级：环境变量 > 配置文件 > 默认值

        Returns:
            str: 静态文件目录路径
        """
        static_dir = self.get('static_dir') or 'static'
        logger.info(f"静态文件目录配置: {static_dir}")
        return static_dir

    def validate_config(self) -> bool:
        """验证配置完整性"""
        try:
            # 验证阿里云凭证
            self.get_aliyun_credentials()

            # 验证API配置
            api_config = self.get_api_config()
            if not api_config['endpoint']:
                raise ValueError("API endpoint不能为空")

            logger.info("配置验证通过")
            return True

        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息摘要"""
        return {
            'environment': self.environment,
            'config_file_loaded': self._config_loaded,
            'config_file_path': self._config_file_path,
            'available_sections': list(self.config.sections()) if self._config_loaded else [],
            'config_sources': {
                'environment_variables': bool(os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID') or os.getenv('ALIYUN_ACCESS_KEY_ID')),
                'config_file': self._config_loaded,
                'defaults': True
            }
        }

# 全局配置管理器实例
_config_manager = None

def get_config_manager(environment: str = None) -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager

    if environment is None:
        environment = os.getenv('AIHUB_ENVIRONMENT', 'default')

    if _config_manager is None or _config_manager.environment != environment:
        _config_manager = ConfigManager(environment)

    return _config_manager

def reload_config(environment: str = None):
    """重新加载配置"""
    global _config_manager
    _config_manager = None
    return get_config_manager(environment)


class Settings:
    """设置对象，提供全局配置访问"""

    def __init__(self):
        self._config_manager = get_config_manager()

    def __getattr__(self, name):
        """动态获取配置值"""
        # 处理特定的配置映射
        if name == 'STATIC_FILE_DIR':
            # 返回静态文件目录
            return self._config_manager.get_static_dir()
        elif name == 'STATIC_URL_PREFIX':
            # 返回静态文件URL前缀
            return '/static'
        elif name == 'API_HOST':
            # 返回API主机
            return self._config_manager.get('host') or '0.0.0.0'
        elif name == 'API_PORT':
            # 返回API端口
            return self._config_manager.get('port') or 5579

        # 其他配置直接从配置管理器获取
        return self._config_manager.get(name.lower())


# 全局设置对象
settings = Settings()
