[Unit]
Description=AI Hub Server
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/aihub-server
Environment=PATH=/opt/aihub-server/venv/bin
Environment=PYTHONPATH=/opt/aihub-server
Environment=FASTAPI_ENV=production
Environment=AIHUB_ENVIRONMENT=production
Environment=AIHUB_HOST=0.0.0.0
Environment=AIHUB_PORT=5579
Environment=AIHUB_THREADS=4
Environment=LOG_DIR=/opt/aihub-server/logs
Environment=LOG_LEVEL=INFO
Environment=LOG_BACKUP_COUNT=30
EnvironmentFile=-/opt/aihub-server/.env
ExecStart=/opt/aihub-server/venv/bin/python -m app.main
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
KillMode=mixed
TimeoutStopSec=30
StandardOutput=journal
StandardError=journal
SyslogIdentifier=aihub-server

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/aihub-server/logs
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
