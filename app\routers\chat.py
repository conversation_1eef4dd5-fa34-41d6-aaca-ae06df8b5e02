# -*- coding: utf-8 -*-
"""
聊天API路由

处理大模型文本交互相关的API请求。
"""

from fastapi import APIRouter, Request

# 导入项目内部模块
from ..services.chat_service import handle_chat_request
from ..models.requests import ChatRequest
from ..models.responses import ChatResponse

# 创建路由器
router = APIRouter(
    prefix="/api/v1/chat",
    tags=["聊天"],
    responses={404: {"description": "Not found"}}
)


@router.post('/new')
async def create_chat_task(request: Request):
    """
    创建大模型文本交互任务端点

    接收POST请求，创建大模型文本交互任务。

    请求体格式:
    {
        "Model": "qwen-plus",  // 可选，默认为"qwen-plus"
        "SysContent": "你是一个AI助手",  // 系统角色的内容
        "UserContent": "你好，请介绍一下自己"  // 用户角色的内容
    }

    Returns:
        JSON响应: 大模型响应结果
    """
    return await handle_chat_request(request)