from docx import Document
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from bs4 import BeautifulSoup
import requests
from io import BytesIO
import base64

def add_toc(doc):
    """Add a Table of Contents field to the document."""
    # Add a paragraph for the TOC
    paragraph = doc.add_paragraph()
    run = paragraph.add_run()
    
    # Create the TOC field code: {TOC \o "1-3" \h \z \u}
    fldChar1 = OxmlElement('w:fldChar')
    fldChar1.set(qn('w:fldCharType'), 'begin')
    
    instrText = OxmlElement('w:instrText')
    instrText.set(qn('xml:space'), 'preserve')
    instrText.text = 'TOC \\o "1-3" \\h \\z \\u'
    
    fldChar2 = OxmlElement('w:fldChar')
    fldChar2.set(qn('w:fldCharType'), 'separate')
    
    fldChar3 = OxmlElement('w:fldChar')
    fldChar3.set(qn('w:fldCharType'), 'end')
    
    # Append the field elements to the run
    run._r.append(fldChar1)
    run._r.append(instrText)
    run._r.append(fldChar2)
    run._r.append(fldChar3)
    
    # Add a note to update the TOC
    doc.add_paragraph('Right-click and select "Update Field" to refresh the Table of Contents.')

def html_to_docx_with_toc(html_content, output_file='output_with_toc.docx'):
    # Create a new Word document
    doc = Document()
    
    # Add a title
    doc.add_heading('Document with Table of Contents', level=1)
    
    # Add the TOC
    add_toc(doc)
    
    # Parse HTML content
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Add a section heading for paragraphs
    doc.add_heading('Paragraphs', level=2)
    
    # Process paragraphs
    for paragraph in soup.find_all('p'):
        doc.add_paragraph(paragraph.get_text())
    
    # Add a section heading for tables
    doc.add_heading('Tables', level=2)
    
    # Process tables
    for table in soup.find_all('table'):
        rows = table.find_all('tr')
        if rows and rows[0].find_all('td'):  # Ensure table has rows and columns
            doc_table = doc.add_table(rows=len(rows), cols=len(rows[0].find_all('td')))
            for i, row in enumerate(rows):
                cells = row.find_all('td')
                for j, cell in enumerate(cells):
                    doc_table.cell(i, j).text = cell.get_text()
    
    # Add a section heading for images
    doc.add_heading('Images', level=2)
    
    # Process images
    for img in soup.find_all('img'):
        img_src = img.get('src')
        if img_src:
            if img_src.startswith('data:image'):  # Handle Base64 images
                img_data = base64.b64decode(img_src.split(',')[1])
                doc.add_picture(BytesIO(img_data))
            else:  # Handle URL images
                try:
                    response = requests.get(img_src)
                    doc.add_picture(BytesIO(response.content))
                except Exception as e:
                    doc.add_paragraph(f"Unable to load image: {img_src} ({str(e)})")
    
    # Save the document
    doc.save(output_file)

# Example HTML content
html_content = """
<html>
<body>
<p>This is a sample paragraph.</p>
<table>
    <tr><td>Row 1, Col 1</td><td>Row 1, Col 2</td></tr>
    <tr><td>Row 2, Col 1</td><td>Row 2, Col 2</td></tr>
</table>
<p>Another paragraph.</p>
<img src="https://via.placeholder.com/150" />
</body>
</html>
"""

# Generate the Word document with TOC
html_to_docx_with_toc(html_content, 'output_with_toc.docx')