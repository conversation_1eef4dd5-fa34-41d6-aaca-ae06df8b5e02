# -*- coding: utf-8 -*-
"""
生成节水型单位文档的API路由

处理节水型单位文档生成相关的API请求。
"""

from fastapi import APIRouter, Request
from pydantic import BaseModel

# 导入项目内部模块
from ..services.wsinst_service import handle_wsinst_request
from ..core.logger import get_logger
from ..models.requests import WsinstRequest
from ..models.responses import WsinstResponse

# 创建路由器
router = APIRouter(
    prefix="/api/v1/wsinst",
    tags=["节水型单位"],
    responses={404: {"description": "Not found"}}
)

# 获取日志记录器
logger = get_logger()


@router.post('/new')
def create_wsinst_task(request: WsinstRequest):
    """
    生成节水型单位文档任务端点.

    接收POST请求，创建生成节水型单位文档任务, 文档类型: docx。

    请求体格式:
    {
        "TaskKey": 字符串, 任务惟一标识符, JSON字符串: "d1fg7skT"
        "Callback":  字符串, 回调URL, JSON字符串: "http://127.0.0.1:5577/api/v1/aihub/wsinst/new/callback"
        "Contents": 字符串, 允许空字符串, docx文档文字内容, JSON字符串: "7、自评报告"
        "Files": 列表, 允许空列表, docx文档附件, JSON字符串: "[{\"name\":\"课程大纲\",\"url\":\"http://**************:5577/assets/proj/2024/12/bo9fe2le4n7wirh4ur/fattach/BO9FE1758787261264.png\",\"mimeType\":\"image/png\"}]"
    }

    支持的文件类型:
    - 图片文件: image/jpeg, image/png, image/gif, image/bmp, image/webp, image/tiff, image/svg+xml
    - PDF文件: application/pdf (将自动转换为图片，每页一张，页间用空行分隔)
    - 其他文件类型将被跳过

    请求体完整例子(JSON str):
    "{\"TaskKey\":\"bo9fe288610ead25\",\"Callback\":\"http://127.0.0.1:5577/api/v1/aihub/wsinst/new/callback\",\"Contents\":\"7、自评报告\",\"Files\":[{\"name\":\"课程大纲\",\"url\":\"http://**************:5577/assets/proj/2024/12/bo9fe2le4n7wirh4ur/fattach/BO9FE1758787261264.png\",\"mimeType\":\"image/png\"},{\"name\":\"photo_2023-03-19_21-45-10\",\"url\":\"http://**************:5577/assets/proj/2024/12/bo9fe2le4n7wirh4ur/fattach/BO9FE1758788945671.jpg\",\"mimeType\":\"image/jpeg\"},{\"name\":\"报告文档\",\"url\":\"http://**************:5577/assets/proj/2024/12/bo9fe2le4n7wirh4ur/fattach/BO9FE1758788945672.pdf\",\"mimeType\":\"application/pdf\"},{\"name\":\"不支持的文件\",\"url\":\"http://**************:5577/assets/proj/2024/12/bo9fe2le4n7wirh4ur/fattach/BO9FE1758788945673.txt\",\"mimeType\":\"text/plain\"}]}"

    Returns:
        JSON响应: 任务创建结果
    {
        "TaskKey": "d1fg7skT",       // 任务惟一标识符
        "RuntimeStatus": "SUCCESS",  // 程序运行状态, "SUCCESS", "FAILED"
        "TaskStatus": "CREATED",     // 任务状态: 'CREATED', 'COMPLETED', 'ONGOING'
    }

    注一: 这个路由使用同步函数, FastAPI会将任务放到线程池中执行, 避免阻塞事件循环.

    注二: 这里使用Pydantic模型和依赖注入来处理请求数据验证和转换, 避免在同步函数中调用`request.json()`这个异步方法.
        **FastAPI会自动完成以下工作**:
        1. 异步地读取请求body
        2. 解析JSON
        3. 验证数据是否符合Item模型的结构和类型
        4. 如果验证失败，自动返回一个详细的422错误响应
        5. 将验证通过的数据转换成一个WsinstRequest对象，注入到你的函数中

        **这种方法的步骤是**:
        1. 定义一个Pydantic模型, 用于定义请求体的结构和数据类型.
        2. 在路由函数中, 使用该模型作为参数, FastAPI会自动从请求体中提取数据并进行验证.
        3. 如果验证成功, 则将数据转换为模型实例, 并将其作为参数传递给处理函数.
        4. 如果验证失败, 则FastAPI会自动返回422 Unprocessable Entity响应, 并包含验证错误信息.

    """
    logger.info("创建节水型单位文档任务")
    logger.info(f"请求参数: {request}")

    request_body = {"TaskKey": request.TaskKey, "Callback": request.Callback, "Contents": request.Contents, "Files": request.Files}
    return handle_wsinst_request(request_body)
