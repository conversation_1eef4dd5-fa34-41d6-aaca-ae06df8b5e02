# AI Hub服务器配置文件
# 此文件包含服务器运行时的各种配置参数
# 配置优先级：环境变量 > 配置文件 > 默认值

[default]
# 服务器网络配置
host = 127.0.0.1
port = 5579
debug = false

# 静态文件配置
base_url = http://127.0.0.1:5579
static_dir = static
docx_subdir = docx

# 性能配置
workers = 4
threads = 4
timeout = 60
connection_limit = 100
cleanup_interval = 30
channel_timeout = 120

# 日志配置
log_dir = logs
log_level = 20
log_level_name = INFO
backup_count = 30

# API配置
endpoint = tingwu.cn-beijing.aliyuncs.com
api_version = 2021-12-21
ssl_verify = true
connect_timeout = 30
read_timeout = 60
max_retries = 3
retry_delay = 1

# 日志记录配置
log_api_calls = true
log_request_data = false
log_response_data = false
mask_sensitive_data = true

# 连接池配置
connection_pool_size = 10
connection_reuse = true
request_queue_size = 100

[development]
# 开发环境配置
host = 127.0.0.1
port = 5579
debug = true
log_level = 10
log_level_name = DEBUG
log_request_data = true
log_response_data = true

# 静态文件配置
base_url = http://127.0.0.1:5579
static_dir = static
docx_subdir = docx

[production]
# 生产环境配置
host = 127.0.0.1
port = 5579
debug = false
workers = 4
threads = 4
connection_limit = 200
log_level = 20
log_level_name = INFO
log_request_data = false
log_response_data = false
mask_sensitive_data = true

# 静态文件配置, 相对于项目根目录
base_url = http://127.0.0.1:5579
static_dir = static
docx_subdir = docx
