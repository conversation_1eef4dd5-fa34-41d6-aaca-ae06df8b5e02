from __future__ import annotations

import io
import requests
from shutil import copy2
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches

from .utils import parse_docx_design
from app.core.logger import get_logger

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from docx.document import Document as DocumentObject


tmp_dir = "tmp/" # 存放临时文件
template_docx = "template.docx" # 模板文件
all_tasks = dict() # 全局任务映射, task_key -> task_info. task_key: str, task_info: dict[str, any]

class WordConverter:
    """
    创建一个DocxConverter类, 用于将HTML表格, 图像, 段落等元素转换为Word文档.
    """
    def __init__(self):
        """
        初始化DocxConverter类, 创建一个用于保存文档任务的字典对象.
        字典对象的键为任务标识(task_key), 值为一个包含任务参数的tasks字典对象.

        tasks字典对象的结构为:
        {
            "task_key": {
                "task_key": "任务惟一标识码",       # 字符串, 等于task_key
                "docx": "docx文档对象",            # docx的Document对象, 用于操作文档
                "tmp_file": "文件路径",            # 字符串
                "task_status": "任务状态",         # 字符串枚举, CREATED: 创建, ONGOING: 处理中, COMPLETED: 完成, FAILED: 失败
                "runtime_status": "程序运行时状态", # 字符串枚举, SUCCESS: 成功, FAILED: 失败
                "contents": "页面定义列表",         # 数组类型
                "meta": "元数据",                  # 可以是任意类型
            }
        }
        """
        self.tmp_dir = tmp_dir
        self.template_docx = template_docx
        # 初始化日志记录器
        self.logger = get_logger(__name__)

    def create_task(self, task_config: dict[str, any]) -> dict[str, any]:
        """
        创建一个任务, 把任务信息保存到tasks中, 生成docx文档, 返回任务信息.
        """
        task_key = task_config.get('TaskKey')
        if not task_key:
            raise ValueError("任务标识 (task_key) 不能为空")
        task_info = all_tasks.get(task_key)
        if task_info:
            raise ValueError(f"任务标识 (task_key) 已存在: {task_key}")
        tmp_file = f"{self.tmp_dir}{task_key}.docx"
        doc = Document(self.template_docx)
        new_task_info = {
            "task_key": task_key,
            "docx": doc,                             # 读取模板文件并初始化Document对象
            "tmp_file": tmp_file,                    # 文档临时文件路径+文件名
            "task_status": "CREATED",                # 任务状态: CREATED, ONGOING, COMPLETED, FAILED.
            "runtime_status": "SUCCESS",             # 程序运行时状态:  SUCCESS, FAILED.
            "contents": task_config.get('Contents'), # 各页面定义, 数组类型
            "meta": task_config,                     # 任务元数据
            }
        all_tasks[task_key] = new_task_info
        doc.save(tmp_file)
        try:
            self.gen_docx(task_key) # gen_docx会保存文件
            new_task_info['task_status'] = 'COMPLETED'  # 成功完成时设置状态
        except Exception as e:
            self.logger.error(f"生成docx文档失败, 任务标识: {task_key}, 错误信息: {e}")
            new_task_info['task_status'] = 'COMPLETED'
            new_task_info['runtime_status'] = 'FAILED'
            new_task_info['meta']['ErrorMsg'] = str(e)
        # 将要实现的任务: 1. 把临时文件放到http文件路径, 2. 把http文件路径放到任务数据中,
        # 注意这些要在上层目录去实现
        # ...
        return task_config

    def get_task(self, task_key: str) -> dict[str, any]:
        """
        根据task_key获取对应的任务信息
        """
        task_info = all_tasks.get(task_key)
        if not task_info:
            raise ValueError(f"任务标识 (task_key) 不存在: {task_key}")
        return task_info

    def remove_task(self, task_key):
        """
        删除一个任务
        """
        task_info = all_tasks.get(task_key)
        if task_info:
            del all_tasks[task_key]
        else:
            raise ValueError(f"任务标识 (task_key) 不存在: {task_key}")

    def save_doc(self, task_key: str) -> str:
        """
        保存Document文档
        """
        task_info = all_tasks.get(task_key)
        if task_info:
            doc = task_info['docx']
            file_path = task_info['tmp_file']
            doc.save(file_path)
            return file_path
        else:
            raise ValueError(f"任务标识 (task_key) 不存在: {task_key}")

    def get_document(self, task_key: str) -> DocumentObject:
        """
        获取一个任务的Document文档对象.
        """
        task_info = all_tasks.get(task_key)
        if task_info:
            return task_info['docx']
        else:
            raise ValueError(f"任务标识 (task_key) 不存在: {task_key}")

    def gen_docx(self, task_key: str) -> str:
        """
        生成docx文档并保存到目录文件.
        """
        doc = self.get_document(task_key)
        contents = self.get_task(task_key).get('contents', [])
        parse_docx_design(doc, contents)

        self.save_doc(task_key)
        return doc
