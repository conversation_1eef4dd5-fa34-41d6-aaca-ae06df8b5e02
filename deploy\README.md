# AI Hub服务器 - Linux部署指南

本文档详细介绍如何在Linux服务器上部署AI Hub服务器，支持多种部署方式。

## 📋 目录

- [系统要求](#系统要求)
- [部署方式](#部署方式)
  - [方式一：自动部署脚本](#方式一自动部署脚本)
  - [方式二：Docker部署](#方式二docker部署)
  - [方式三：手动部署](#方式三手动部署)
- [服务管理](#服务管理)
- [Nginx反向代理](#nginx反向代理)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)
- [性能优化](#性能优化)
- [安全配置](#安全配置)

## 🖥️ 系统要求

### 最低要求
- **操作系统**: Ubuntu 18.04+, CentOS 7+, Debian 9+, RHEL 7+
- **CPU**: 1核心
- **内存**: 1GB RAM
- **存储**: 5GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8
- **CPU**: 2核心或更多
- **内存**: 2GB RAM 或更多
- **存储**: 20GB 可用空间
- **网络**: 带宽 ≥ 10Mbps

### 软件依赖
- Python 3.10+
- pip (Python包管理器)
- systemd (服务管理)
- curl (用于健康检查)
- 可选: Docker & Docker Compose
- 可选: Nginx (反向代理)

## 🚀 部署方式

### 方式一：自动部署脚本

**最简单的部署方式，推荐用于生产环境。**

#### 1. 下载部署文件

```bash
# 创建部署目录
sudo mkdir -p /tmp/aihub-deploy
cd /tmp/aihub-deploy

# 上传部署文件到服务器
# 将整个项目目录上传到服务器
scp -r ./aihub user@your-server:/tmp/aihub-deploy/
```

#### 2. 运行部署脚本

```bash
# 进入部署目录
cd /tmp/aihub-deploy/deploy

# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
sudo ./deploy.sh
```

#### 3. 配置阿里云凭证

```bash
# 编辑配置文件
sudo mkdir -p /opt/aihub-server/config
sudo nano /opt/aihub-server/config/credentials.conf

# 填入阿里云凭证
[default]
access_key_id = your_access_key_id
access_key_secret = your_access_key_secret
region_id = cn-beijing
```

或者使用环境变量：

```bash
# 编辑环境变量文件
sudo nano /opt/aihub-server/.env

# 填入阿里云凭证
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
ALIBABA_CLOUD_REGION_ID=cn-beijing
```

#### 4. 重启服务

```bash
# 重启服务使配置生效
sudo systemctl restart tingwu-server

# 检查服务状态
sudo systemctl status tingwu-server
```

### 方式二：Docker部署

**适合容器化环境，便于扩展和管理。**

#### 1. 安装Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 准备部署文件

```bash
# 创建部署目录
mkdir -p ~/tingwu-server
cd ~/tingwu-server

# 上传项目文件
# 将项目文件上传到当前目录
```

#### 3. 配置环境变量

```bash
# 复制环境变量模板
cp deploy/.env.example .env

# 编辑环境变量
nano .env

# 填入必要配置
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
```

#### 4. 启动服务

```bash
# 构建并启动服务
docker-compose -f deploy/docker-compose.yml up -d

# 查看服务状态
docker-compose -f deploy/docker-compose.yml ps

# 查看日志
docker-compose -f deploy/docker-compose.yml logs -f
```

#### 5. 启动Nginx (可选)

```bash
# 启动包含Nginx的完整服务
docker-compose -f deploy/docker-compose.yml --profile with-nginx up -d
```

### 方式三：手动部署

**适合需要自定义配置的场景。**

#### 1. 创建用户和目录

```bash
# 创建应用用户
sudo useradd --system --shell /bin/false --home-dir /opt/tingwu-server --create-home tingwu

# 创建必要目录
sudo mkdir -p /opt/tingwu-server/{logs,config}
sudo chown -R tingwu:tingwu /opt/tingwu-server
```

#### 2. 安装Python依赖

```bash
# 安装系统依赖
sudo apt update
sudo apt install -y python3 python3-pip python3-venv python3-dev build-essential

# 切换到应用目录
cd /opt/tingwu-server

# 创建虚拟环境
sudo -u tingwu python3 -m venv venv

# 激活虚拟环境并安装依赖
sudo -u tingwu bash -c "source venv/bin/activate && pip install --upgrade pip"
sudo -u tingwu bash -c "source venv/bin/activate && pip install -r requirements.txt"
```

#### 3. 复制应用文件

```bash
# 复制应用文件
sudo cp -r /path/to/tingwu_server /opt/tingwu-server/
sudo cp /path/to/requirements.txt /opt/tingwu-server/
sudo cp /path/to/setup.py /opt/tingwu-server/

# 设置权限
sudo chown -R tingwu:tingwu /opt/tingwu-server
```

#### 4. 创建systemd服务

```bash
# 复制服务文件
sudo cp deploy/tingwu-server.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload
sudo systemctl enable tingwu-server
```

#### 5. 配置环境变量

```bash
# 创建环境变量文件
sudo cp deploy/.env.example /opt/tingwu-server/.env
sudo nano /opt/tingwu-server/.env

# 设置权限
sudo chown tingwu:tingwu /opt/tingwu-server/.env
sudo chmod 600 /opt/tingwu-server/.env
```

#### 6. 启动服务

```bash
# 启动服务
sudo systemctl start tingwu-server

# 检查状态
sudo systemctl status tingwu-server
```

## 🔧 服务管理

### 使用管理脚本

```bash
# 复制管理脚本
sudo cp deploy/manage.sh /usr/local/bin/tingwu-manage
sudo chmod +x /usr/local/bin/tingwu-manage

# 使用管理脚本
tingwu-manage start     # 启动服务
tingwu-manage stop      # 停止服务
tingwu-manage restart   # 重启服务
tingwu-manage status    # 查看状态
tingwu-manage logs      # 查看日志
tingwu-manage test      # 测试API
```

### 直接使用systemctl

```bash
# 基本操作
sudo systemctl start tingwu-server
sudo systemctl stop tingwu-server
sudo systemctl restart tingwu-server
sudo systemctl reload tingwu-server

# 查看状态和日志
sudo systemctl status tingwu-server
sudo journalctl -u tingwu-server -f

# 开机自启
sudo systemctl enable tingwu-server
sudo systemctl disable tingwu-server
```

## 🌐 Nginx反向代理

### 安装Nginx

```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo yum install -y nginx
```

### 配置Nginx

```bash
# 复制配置文件
sudo cp deploy/nginx.conf /etc/nginx/sites-available/tingwu-server

# 创建软链接
sudo ln -s /etc/nginx/sites-available/tingwu-server /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### SSL证书配置

```bash
# 创建SSL目录
sudo mkdir -p /etc/nginx/ssl

# 生成自签名证书 (仅用于测试)
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/server.key \
    -out /etc/nginx/ssl/server.crt

# 或者使用Let's Encrypt (推荐)
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 📊 监控和日志

### 日志功能

千义听悟服务器内置了完善的日志系统，支持按天分割的日志文件：

**日志特性：**
- 按天自动分割日志文件
- 自动清理过期日志文件
- 支持多种日志级别 (DEBUG/INFO/WARNING/ERROR)
- 详细记录API请求、响应和错误信息
- 脱敏处理敏感信息

**日志配置：**
```bash
# 在.env文件中配置
LOG_LEVEL=INFO          # 日志级别
LOG_DIR=logs            # 日志目录
LOG_BACKUP_COUNT=30     # 日志文件保留天数
```

### 日志位置

```bash
# 系统日志
sudo journalctl -u tingwu-server -f

# 应用日志 (按天分割)
tail -f /opt/tingwu-server/logs/tingwu-server-$(date +%Y-%m-%d).log

# 查看所有日志文件
ls -la /opt/tingwu-server/logs/

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 日志内容示例

```
2024-01-15 10:30:15,123 - tingwu_server - INFO - 服务器启动完成，监听地址: 0.0.0.0:5579
2024-01-15 10:30:25,456 - tingwu_server - INFO - HTTP请求开始 - POST /api/v1/voice/tingwu/new
2024-01-15 10:30:25,457 - tingwu_server - INFO - 请求数据: {"taskKey": "task_123", "fileUrl": "http://***"}
2024-01-15 10:30:26,789 - tingwu_server - INFO - API调用成功，TaskId: abc123, 耗时: 1.332s
2024-01-15 10:30:26,790 - tingwu_server - INFO - HTTP请求完成 - 200, 耗时: 1.334s
```

### 健康检查

```bash
# 检查服务健康状态
curl http://localhost:5579/health

# 通过Nginx检查
curl http://your-domain.com/health
```

### 性能监控

```bash
# 查看系统资源使用
top
htop
free -h
df -h

# 查看网络连接
ss -tlnp | grep 5579
netstat -tlnp | grep 5579
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细错误信息
sudo journalctl -u tingwu-server -n 50

# 检查配置文件
sudo -u tingwu /opt/tingwu-server/venv/bin/python -m tingwu_server.app --check-config

# 检查端口占用
sudo ss -tlnp | grep 5579
```

#### 2. API认证失败

```bash
# 检查环境变量
sudo cat /opt/tingwu-server/.env | grep ALIBABA_CLOUD

# 测试阿里云凭证
sudo -u tingwu bash -c "source /opt/tingwu-server/venv/bin/activate && python -m tingwu_server.cli_tool --test"
```

#### 3. 内存不足

```bash
# 检查内存使用
free -h

# 添加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### 4. 磁盘空间不足

```bash
# 检查磁盘使用
df -h

# 清理日志文件
sudo journalctl --vacuum-time=7d
sudo find /opt/tingwu-server/logs -name "*.log.*" -mtime +7 -delete
```

### 调试模式

```bash
# 启用调试模式
sudo systemctl stop tingwu-server
sudo -u tingwu bash -c "cd /opt/tingwu-server && source venv/bin/activate && FLASK_ENV=development python -m tingwu_server.app"
```

## ⚡ 性能优化

### 系统优化

```bash
# 调整文件描述符限制
echo "tingwu soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "tingwu hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 调整内核参数
echo "net.core.somaxconn = 1024" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 1024" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 应用优化

```bash
# 调整工作线程数
sudo nano /opt/tingwu-server/.env
# 设置 TINGWU_THREADS=4 (根据CPU核心数调整)

# 启用连接池
# 在.env中添加:
# TINGWU_CONNECTION_POOL_SIZE=20
```

### 负载均衡

```bash
# 运行多个实例
sudo cp /etc/systemd/system/tingwu-server.service /etc/systemd/system/tingwu-server@.service

# 修改服务文件支持实例参数
sudo nano /etc/systemd/system/tingwu-server@.service
# 修改端口为 5579%i

# 启动多个实例
sudo systemctl start tingwu-server@1
sudo systemctl start tingwu-server@2

# 在Nginx中配置负载均衡
# 编辑 /etc/nginx/sites-available/tingwu-server
# 在upstream块中添加多个server
```

## 🔒 安全配置

### 防火墙配置

```bash
# Ubuntu (UFW)
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw allow 5579/tcp    # 应用端口 (如果直接访问)
sudo ufw enable

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=5579/tcp
sudo firewall-cmd --reload
```

### SSL/TLS配置

```bash
# 使用强密码套件
# 在nginx.conf中已配置现代SSL设置

# 定期更新证书
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 访问控制

```bash
# 限制API访问 (在.env中配置)
TINGWU_ALLOWED_IPS=***********/24,10.0.0.0/8
TINGWU_API_KEY=your_secure_api_key
```

### 日志安全

```bash
# 设置日志文件权限
sudo chmod 640 /opt/tingwu-server/logs/*.log
sudo chown tingwu:adm /opt/tingwu-server/logs/*.log

# 配置日志轮转
sudo nano /etc/logrotate.d/tingwu-server
```

## 📝 维护任务

### 定期备份

```bash
# 创建备份脚本
sudo nano /usr/local/bin/tingwu-backup.sh

#!/bin/bash
BACKUP_DIR="/backup/tingwu-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r /opt/tingwu-server/config "$BACKUP_DIR/"
cp /opt/tingwu-server/.env "$BACKUP_DIR/"
cp /etc/systemd/system/tingwu-server.service "$BACKUP_DIR/"
tar -czf "$BACKUP_DIR.tar.gz" "$BACKUP_DIR"
rm -rf "$BACKUP_DIR"

# 设置定时备份
sudo crontab -e
# 添加: 0 2 * * 0 /usr/local/bin/tingwu-backup.sh
```

### 更新应用

```bash
# 停止服务
sudo systemctl stop tingwu-server

# 备份当前版本
sudo cp -r /opt/tingwu-server /opt/tingwu-server.backup.$(date +%Y%m%d)

# 更新代码
# 上传新版本文件

# 更新依赖
sudo -u tingwu bash -c "cd /opt/tingwu-server && source venv/bin/activate && pip install -r requirements.txt"

# 重启服务
sudo systemctl start tingwu-server
```

## 📞 技术支持

如果在部署过程中遇到问题，请检查：

1. **系统日志**: `sudo journalctl -u tingwu-server -f`
2. **应用日志**: `/opt/tingwu-server/logs/tingwu-server.log`
3. **配置文件**: `/opt/tingwu-server/.env`
4. **网络连接**: 确保可以访问阿里云API
5. **权限设置**: 确保tingwu用户有正确的文件权限

---

**部署完成后，请访问 `http://your-server:5579/health` 验证服务是否正常运行。**