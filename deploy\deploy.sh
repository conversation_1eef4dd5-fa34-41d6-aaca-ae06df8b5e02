#!/bin/bash

# AI Hub服务器 - Linux部署脚本
# 作者: Assistant
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="aihub-server"
APP_DIR="/opt/${APP_NAME}"
APP_USER="www-data"
APP_GROUP="www-data"
PYTHON_VERSION="3.10"
SERVICE_NAME="aihub-server"
PORT=5579

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统类型
check_system() {
    log_info "检查系统类型..."

    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
        log_info "检测到系统: $OS $VER"
    else
        log_error "无法检测系统类型"
        exit 1
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."

    if command -v apt-get >/dev/null 2>&1; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y python3 python3-pip python3-venv python3-dev \
                          build-essential curl wget git nginx supervisor \
                          software-properties-common
    elif command -v yum >/dev/null 2>&1; then
        # CentOS/RHEL
        yum update -y
        yum install -y python3 python3-pip python3-devel gcc gcc-c++ \
                      curl wget git nginx supervisor
    elif command -v dnf >/dev/null 2>&1; then
        # Fedora
        dnf update -y
        dnf install -y python3 python3-pip python3-devel gcc gcc-c++ \
                      curl wget git nginx supervisor
    else
        log_error "不支持的包管理器"
        exit 1
    fi

    log_success "系统依赖安装完成"
}

# 创建应用用户
create_app_user() {
    log_info "创建应用用户..."

    if ! id "$APP_USER" &>/dev/null; then
        useradd --system --shell /bin/false --home-dir $APP_DIR \
                --create-home --user-group $APP_USER
        log_success "用户 $APP_USER 创建成功"
    else
        log_info "用户 $APP_USER 已存在"
    fi
}

# 创建应用目录
create_app_directory() {
    log_info "创建应用目录..."

    mkdir -p $APP_DIR
    mkdir -p $APP_DIR/logs
    mkdir -p $APP_DIR/config
    mkdir -p /var/log/tingwu-server

    chown -R $APP_USER:$APP_GROUP $APP_DIR
    chown -R $APP_USER:$APP_GROUP /var/log/tingwu-server
    chmod 755 $APP_DIR
    chmod 755 /var/log/tingwu-server
    chmod 755 $APP_DIR/config

    log_success "应用目录创建完成"
}

# 复制应用文件
copy_app_files() {
    log_info "复制应用文件..."

    # 获取脚本所在目录
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
    SOURCE_DIR="$(dirname "$SCRIPT_DIR")"

    # 复制应用文件
    cp -r "$SOURCE_DIR/tingwu_server" $APP_DIR/
    cp "$SOURCE_DIR/requirements.txt" $APP_DIR/
    cp "$SOURCE_DIR/setup.py" $APP_DIR/
    cp "$SOURCE_DIR/README.md" $APP_DIR/
    cp "$SOURCE_DIR/config_example.py" $APP_DIR/config/
    cp "$SOURCE_DIR/credentials.conf" $APP_DIR/config/

    # 设置权限
    chown -R $APP_USER:$APP_GROUP $APP_DIR
    find $APP_DIR -type f -exec chmod 644 {} \;
    find $APP_DIR -type d -exec chmod 755 {} \;

    # 设置配置文件权限
    log_info "设置配置文件权限..."
    chmod 600 $APP_DIR/config/credentials.conf
    chown $APP_USER:$APP_GROUP $APP_DIR/config/credentials.conf

    log_success "应用文件复制完成"
}

# 创建Python虚拟环境
create_virtualenv() {
    log_info "创建Python虚拟环境..."

    cd $APP_DIR
    sudo -u $APP_USER python3 -m venv venv

    # 激活虚拟环境并安装依赖
    sudo -u $APP_USER bash -c "source venv/bin/activate && pip install --upgrade pip"
    sudo -u $APP_USER bash -c "source venv/bin/activate && pip install -r requirements.txt"

    log_success "Python虚拟环境创建完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."

    # 创建环境变量文件
    cat > $APP_DIR/.env << EOF
# 阿里云凭证配置
# ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
# ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
# ALIBABA_CLOUD_REGION_ID=cn-beijing

# 服务器配置
AIHUB_HOST=0.0.0.0
AIHUB_PORT=$PORT
AIHUB_THREADS=4
FASTAPI_ENV=production

# 日志配置
AIHUB_LOG_LEVEL=INFO
AIHUB_LOG_FILE=$APP_DIR/logs/aihub-server.log
EOF

    chown $APP_USER:$APP_GROUP $APP_DIR/.env
    chmod 600 $APP_DIR/.env

    log_success "配置文件创建完成"
    log_warning "请编辑 $APP_DIR/.env 文件，配置阿里云凭证"
}

# 安装systemd服务
install_systemd_service() {
    log_info "安装systemd服务..."

    # 获取脚本所在目录
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

    # 复制服务文件
    cp "$SCRIPT_DIR/tingwu-server.service" /etc/systemd/system/

    # 重新加载systemd
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME

    log_success "systemd服务安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."

    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu防火墙
        ufw allow $PORT/tcp
        log_success "UFW防火墙规则添加完成"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL防火墙
        firewall-cmd --permanent --add-port=$PORT/tcp
        firewall-cmd --reload
        log_success "firewalld防火墙规则添加完成"
    else
        log_warning "未检测到防火墙，请手动开放端口 $PORT"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."

    systemctl start $SERVICE_NAME
    sleep 3

    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "服务启动成功"
        systemctl status $SERVICE_NAME --no-pager
    else
        log_error "服务启动失败"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 测试服务
test_service() {
    log_info "测试服务..."

    sleep 5

    if curl -s "http://localhost:$PORT/health" >/dev/null; then
        log_success "服务测试通过"
        curl -s "http://localhost:$PORT/health" | python3 -m json.tool
    else
        log_error "服务测试失败"
        log_info "请检查服务状态: systemctl status $SERVICE_NAME"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "应用目录: $APP_DIR"
    echo "服务名称: $SERVICE_NAME"
    echo "监听端口: $PORT"
    echo "运行用户: $APP_USER"
    echo
    echo "=== 常用命令 ==="
    echo "启动服务: systemctl start $SERVICE_NAME"
    echo "停止服务: systemctl stop $SERVICE_NAME"
    echo "重启服务: systemctl restart $SERVICE_NAME"
    echo "查看状态: systemctl status $SERVICE_NAME"
    echo "查看日志: journalctl -u $SERVICE_NAME -f"
    echo "应用日志: tail -f $APP_DIR/logs/tingwu-server.log"
    echo
    echo "=== 配置文件 ==="
    echo "环境变量: $APP_DIR/.env"
    echo "服务配置: /etc/systemd/system/tingwu-server.service"
    echo
    echo "=== 重要提醒 ==="
    echo "1. 请编辑 $APP_DIR/.env 文件，配置阿里云凭证"
    echo "2. 配置完成后重启服务: systemctl restart $SERVICE_NAME"
    echo "3. 健康检查: curl http://localhost:$PORT/health"
    echo "4. API测试: curl -X POST http://localhost:$PORT/api/v1/voice/tingwu/new"
}

# 主函数
main() {
    log_info "开始部署千义听悟音频转写服务器..."

    check_root
    check_system
    install_system_deps
    create_app_user
    create_app_directory
    copy_app_files
    create_virtualenv
    create_config
    install_systemd_service
    configure_firewall
    start_service
    test_service
    show_deployment_info

    log_success "部署完成！"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
