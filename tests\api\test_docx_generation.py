#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文档生成API测试

测试DOCX文档生成相关的API接口和核心功能
"""

import pytest
import requests
import json
import os
import time
import math
from pathlib import Path
from urllib.request import urlretrieve
from docx import Document

# 导入应用模块
from app.gendoc.parse_html import parse_html_dom


class TestDocxGenerationAPI:
    """DOCX文档生成API测试类"""
    
    @pytest.fixture
    def api_url(self):
        """API服务器地址"""
        return "http://localhost:5579/api/v1/docxgen/new"
    
    @pytest.fixture
    def api_request_data(self, sample_html_content):
        """构造API请求数据"""
        if not sample_html_content:
            pytest.skip("测试数据content.html不存在")
            
        return {
            "TaskKey": f"test_api_{math.floor(time.time())}",
            "Callback": "http://127.0.0.1:5577/api/v1/aihub/docxgen/new/callback",
            "Contents": [{
                "type": "Html",
                "content": sample_html_content,
                "orient": "Landscape"
            }],
        }
    
    def test_api_docx_generation_success(self, api_url, api_request_data, tmp_path):
        """测试API成功生成DOCX文档"""
        try:
            response = requests.post(
                api_url,
                json=api_request_data,
                headers={'Content-Type': 'application/json'},
                timeout=300
            )
            
            if response.status_code == 200:
                # 验证响应结构
                response_data = response.json()
                assert 'Data' in response_data
                assert 'FileUrl' in response_data['Data']
                
                # 下载生成的文档
                file_url = response_data['Data']['FileUrl']
                output_file = tmp_path / 'api_test_result.docx'
                urlretrieve(file_url, str(output_file))
                
                # 验证文件存在且不为空
                assert output_file.exists()
                assert output_file.stat().st_size > 0
                
                # 验证文档可以正常打开
                doc = Document(str(output_file))
                assert len(doc.paragraphs) > 0
                
            else:
                pytest.fail(f"API请求失败，状态码: {response.status_code}, 错误信息: {response.text}")
                
        except requests.exceptions.ConnectionError:
            pytest.skip("API服务器不可用，跳过API测试")
        except Exception as e:
            pytest.fail(f"API测试过程中发生错误: {e}")
    
    def test_api_request_validation(self, api_url):
        """测试API请求参数验证"""
        try:
            # 测试空请求
            response = requests.post(
                api_url,
                json={},
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            # 应该返回错误状态码
            assert response.status_code != 200
            
        except requests.exceptions.ConnectionError:
            pytest.skip("API服务器不可用，跳过API验证测试")


class TestDocxGenerationCore:
    """DOCX文档生成核心功能测试类"""
    
    def test_parse_html_dom_basic(self, sample_html_content, temp_docx_path):
        """测试parse_html_dom基本功能"""
        if not sample_html_content:
            pytest.skip("测试数据content.html不存在")
        
        # 构造测试数据 - 直接传递给parse_html_dom的格式
        html_data = {
            "type": "HTML",
            "content": sample_html_content
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, html_data)
        result_doc.save(str(temp_docx_path))
        
        # 验证文档生成成功
        assert len(result_doc.paragraphs) > 0
    
    def test_parse_html_dom_no_empty_first_line(self, sample_html_content, temp_docx_path):
        """测试parse_html_dom修复第一行空行问题"""
        if not sample_html_content:
            pytest.skip("测试数据content.html不存在")
        
        # 构造测试数据 - 直接传递给parse_html_dom的格式
        html_data = {
            "type": "HTML",
            "content": sample_html_content
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, html_data)
        result_doc.save(str(temp_docx_path))
        
        # 验证第一行不为空
        if result_doc.paragraphs:
            first_paragraph = result_doc.paragraphs[0]
            assert first_paragraph.text.strip() != "", "第一行不应该为空"
    
    def test_parse_html_dom_landscape_orientation(self, sample_html_content, temp_docx_path):
        """测试横向布局功能"""
        if not sample_html_content:
            pytest.skip("测试数据content.html不存在")
        
        # 构造测试数据 - 直接传递给parse_html_dom的格式
        html_data = {
            "type": "HTML",
            "content": sample_html_content,
            "orient": "landscape"  # 设置横向布局
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, html_data)
        result_doc.save(str(temp_docx_path))
        
        # 验证横向布局设置
        section = result_doc.sections[0]
        # 横向布局时，宽度应该大于高度
        assert section.page_width > section.page_height, "横向布局设置失败"
    
    def test_document_structure_analysis(self, sample_html_content, temp_docx_path):
        """测试文档结构分析功能"""
        if not sample_html_content:
            pytest.skip("测试数据content.html不存在")
        
        # 构造测试数据 - 直接传递给parse_html_dom的格式
        html_data = {
            "type": "HTML",
            "content": sample_html_content
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, html_data)
        result_doc.save(str(temp_docx_path))
        
        # 分析文档结构
        paragraphs = result_doc.paragraphs
        assert len(paragraphs) > 0, "文档应该包含段落"
        
        # 显示前5个段落的内容（用于调试）
        print("\n=== 文档结构分析 ===")
        for i, para in enumerate(paragraphs[:5]):
            print(f"段落 {i+1}: '{para.text[:50]}...'" if len(para.text) > 50 else f"段落 {i+1}: '{para.text}'")
        
        # 验证文档包含表格内容
        full_text = " ".join([p.text for p in paragraphs])
        assert "水平衡测试表" in full_text or "用水单位" in full_text, "文档应该包含测试数据的关键内容"


class TestDocxGenerationEdgeCases:
    """DOCX文档生成边界情况测试类"""
    
    def test_empty_html_content(self, temp_docx_path):
        """测试空HTML内容处理"""
        api_data = {
            "Contents": [{
                "type": "Html",
                "content": "",
                "orient": "Portrait"
            }],
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, api_data)
        result_doc.save(str(temp_docx_path))
        
        # 验证空内容也能正常处理
        assert temp_docx_path.exists()
    
    def test_invalid_html_content(self, temp_docx_path):
        """测试无效HTML内容处理"""
        api_data = {
            "Contents": [{
                "type": "Html",
                "content": "<invalid><html><content>",
                "orient": "Portrait"
            }],
        }
        
        doc = Document()
        # 应该能够处理无效HTML而不崩溃
        result_doc = parse_html_dom(doc, api_data)
        result_doc.save(str(temp_docx_path))
        
        assert temp_docx_path.exists()
    
    def test_multiple_content_blocks(self, temp_docx_path):
        """测试多个内容块的处理"""
        # 构造包含多个内容块的测试数据 - 合并为单个HTML内容
        html_data = {
            "type": "HTML",
            "content": "<h1>第一部分</h1><p>这是第一个内容块</p><h2>第二部分</h2><p>这是第二个内容块</p>"
        }
        
        doc = Document()
        result_doc = parse_html_dom(doc, html_data)
        result_doc.save(str(temp_docx_path))
        
        # 验证多个内容块都被处理
        content_text = " ".join([p.text for p in result_doc.paragraphs])
        assert "第一部分" in content_text
        assert "第二部分" in content_text