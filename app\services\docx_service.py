# -*- coding: utf-8 -*-
"""
Docx文档生成模块

提供docx文档生成的HTTP请求处理功能，包括参数验证、文档生成和响应处理。
基于WordConverter类实现docx文档的创建和处理。
"""

import json
import time
import os
import shutil
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from ..core.logger import get_logger, log_request, log_api_call
from ..gendoc.word import WordConverter
from ..core.config import ConfigManager
from ..models.requests import validate_docxgen_request
from ..utils.http import create_error_response, create_success_response


def handle_docxgen_request(request_body: dict) -> JSONResponse:
    """
    处理docx文档生成请求

    处理POST请求到/api/v1/docxgen/new端点，提取请求参数，
    调用WordConverter创建docx文档生成任务，并返回结果。

    Args:
        request_body: 包含请求参数的字典, 包含以下键:
            - TaskKey: 任务唯一标识
            - Callback: 回调URL
            - Contents: 文档内容数组

    Returns:
        JSONResponse: FastAPI响应对象，包含JSON格式的结果
    """
    # 记录请求开始时间
    start_time = time.time()

    # 获取日志记录器
    logger = get_logger()

    data = request_body
    try:
        # 记录请求日志
        log_request('POST', '/api/v1/docxgen/new')

        # 记录请求数据（隐藏敏感信息）
        safe_data = {k: v for k, v in data.items() if k not in ['Callback']}
        safe_data['Callback'] = '***' if 'Callback' in data else None
        safe_data['Contents'] = '***'
        logger.info(f"收到创建docx生成任务请求: {json.dumps(safe_data, ensure_ascii=False)}")

        # 使用Pydantic验证请求参数
        try:
            validated_request = validate_docxgen_request(data)
            logger.info("docx生成请求参数验证通过")
        except ValidationError as e:
            # 格式化Pydantic验证错误信息
            error_details = []
            for error in e.errors():
                field = '.'.join(str(loc) for loc in error['loc'])
                message = error['msg']
                error_details.append(f"{field}: {message}")
            validation_error = "; ".join(error_details)
            logger.error(f"docx生成请求参数验证失败: {validation_error}")
            return create_error_response(f"请求参数验证失败: {validation_error}", 400)
        except Exception as e:
            validation_error = str(e)
            logger.error(f"docx生成请求参数验证失败: {validation_error}")
            return create_error_response(f"请求参数验证失败: {validation_error}", 400)

        # 从验证后的请求中提取参数
        task_key = validated_request.TaskKey
        callback_url = str(validated_request.Callback)
        contents_data = validated_request.Contents

        # Contents现在直接是数组类型，无需JSON解析
        logger.info(f"Contents获取成功，包含 {len(contents_data)} 个元素")

        # 创建WordConverter实例，设置正确的模板路径
        try:
            word_converter = WordConverter()
            # 设置模板文件和临时目录的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            father_dir = os.path.dirname(current_dir)
            grandfather_dir = os.path.dirname(father_dir)
            template_path = os.path.join(father_dir, 'gendoc', 'template.docx')
            tmp_dir = os.path.join(grandfather_dir, 'tmp')

            # 确保临时目录存在
            os.makedirs(tmp_dir, exist_ok=True)

            word_converter.template_docx = template_path
            word_converter.tmp_dir = tmp_dir + os.sep  # 添加路径分隔符
            logger.info(f"WordConverter实例创建成功，模板路径: {template_path}，临时目录: {tmp_dir}")
        except Exception as e:
            logger.error(f"创建WordConverter实例失败: {str(e)}")
            return create_error_response(f"初始化文档转换器失败: {str(e)}", 500)

        # 准备任务配置
        task_config = {
            'TaskKey': task_key,
            'Callback': callback_url,
            'Contents': contents_data
        }

        # 记录API调用参数
        api_params = {
            'task_key': task_key,
            'callback_url': callback_url,
            'contents_count': len(contents_data)
        }

        logger.info(f"开始创建docx生成任务，TaskKey: {task_key}")

        # 调用WordConverter创建任务
        try:
            result = word_converter.create_task(task_config)
            logger.info(f"docx生成任务创建成功，TaskKey: {task_key}")

            # 获取任务信息以确定实际状态
            task_info = word_converter.get_task(task_key)
            actual_task_status = task_info.get('task_status', 'CREATED')
            actual_runtime_status = task_info.get('runtime_status', 'SUCCESS')

            # 如果任务完成（无论runtime_status如何），且文件存在，处理文件拷贝和URL生成
            file_url = None
            if actual_task_status == 'COMPLETED':
                try:
                    # 初始化配置管理器
                    config_manager = ConfigManager()

                    # 从配置文件读取静态目录配置，默认为'static'
                    static_dir = config_manager.get('static_dir') or 'static'

                    # 从配置文件读取docx子目录配置，默认为'docx'
                    docx_subdir = config_manager.get('docx_subdir') or 'docx'

                    # 从配置文件读取对外访问根URL，默认为'http://127.0.0.1:5579'
                    base_url = config_manager.get('base_url') or 'http://127.0.0.1:5579'

                    # 构建静态目录下的docx子目录路径
                    # 从当前文件路径向上退三层目录到达项目根目录
                    # docx_service.py -> services -> app -> aihub (项目根目录)
                    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    static_docx_dir = os.path.join(project_root, static_dir, docx_subdir)

                    # 确保静态目录和docx子目录存在
                    os.makedirs(static_docx_dir, exist_ok=True)
                    logger.info(f"确保静态目录存在: {static_docx_dir}")

                    # 获取临时文件路径
                    tmp_file_path = task_info.get('tmp_file')
                    if tmp_file_path and os.path.exists(tmp_file_path):
                        # 构建目标文件路径
                        docx_filename = f"{task_key}.docx"
                        target_file_path = os.path.join(static_docx_dir, docx_filename)

                        # 拷贝文件到静态目录
                        shutil.copy2(tmp_file_path, target_file_path)
                        logger.info(f"成功拷贝docx文件: {tmp_file_path} -> {target_file_path}")

                        # 生成对外访问地址
                        file_url = f"{base_url.rstrip('/')}/{static_dir}/{docx_subdir}/{docx_filename}"
                        logger.info(f"生成docx文件访问地址: {file_url}")
                    else:
                        logger.warning(f"临时文件不存在或路径无效: {tmp_file_path}")

                except Exception as e:
                    logger.error(f"处理docx文件拷贝和URL生成失败: {str(e)}")
                    # 不影响主要任务流程，继续执行

        except ValueError as e:
            # 处理业务逻辑错误（如TaskKey已存在）
            error_msg = f"创建docx生成任务失败: {str(e)}"
            logger.error(error_msg)
            return create_error_response(error_msg, 400)
        except Exception as e:
            # 处理其他异常
            error_msg = f"创建docx生成任务失败: {str(e)}"
            logger.error(error_msg)
            return create_error_response(error_msg, 500)

        # 计算响应时间
        response_time = time.time() - start_time

        # 记录成功的API调用
        log_api_call(
            'create_docx_task',
            api_params,
            {
                'task_key': task_key,
                'status': 'success',
                'task_status': actual_task_status,
                'runtime_status': actual_runtime_status
            }
        )
        log_request('POST', '/api/v1/docxgen/new', response_time=response_time)

        logger.info(f"docx生成任务处理完成，TaskKey: {task_key}, 耗时: {response_time:.3f}s")

        # 构建响应数据，使用实际的任务状态
        response_data = {
            "TaskKey": task_key,
            "RuntimeStatus": actual_runtime_status,
            "TaskStatus": actual_task_status,
            "Duration": response_time,
        }

        # 如果生成了文件访问地址，添加到响应数据中
        if file_url:
            response_data["FileUrl"] = file_url

        # 使用标准化成功响应格式
        success_response = create_success_response(
            response_data,
            "docx生成任务完成，文件已生成"
        )

        # 返回JSON响应
        return JSONResponse(
            content=success_response,
            media_type='application/json; charset=utf-8'
        )

    except Exception as e:
        # 处理未预期的异常
        error_msg = f"处理docx生成请求失败: {str(e)}"
        response_time = time.time() - start_time

        # 记录失败的API调用
        if 'task_key' in locals():
            log_api_call(
                'create_docx_task',
                api_params if 'api_params' in locals() else {},
                error=error_msg
            )

        log_request('POST', '/api/v1/docxgen/new', response_time=response_time)
        logger.error(f"{error_msg}, 耗时: {response_time:.3f}s")

        return create_error_response(error_msg, 500)