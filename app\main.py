# -*- coding: utf-8 -*-
"""
AI Hub服务器启动脚本

通用启动脚本，支持开发和生产环境的差异化配置。
根据环境变量自动调整服务器参数和日志级别。
"""

import os
import sys
import uvicorn
from .app import create_app, check_python_version, check_dependencies, check_credentials
from .core.logger import setup_logging, get_logger, cleanup_old_logs
from .core.config import get_config_manager

# 获取环境配置
config_manager = get_config_manager()  # 自动从环境变量或默认值获取环境
environment = config_manager.environment

# 获取日志配置
logging_config = config_manager.get_logging_config()

# 初始化日志系统
setup_logging(
    log_dir=logging_config.get('log_dir', 'logs'),
    log_level=logging_config.get('log_level', 20),
    backup_count=logging_config.get('backup_count', 30)
)
logger = get_logger()

# 清理旧日志文件
cleanup_old_logs(logging_config.get('log_dir', 'logs'), logging_config.get('backup_count', 30))

# 记录配置信息
config_info = config_manager.get_config_info()
logger.info(f"服务器启动，环境: {environment}, 配置来源: {config_info['config_sources']}")
if config_info.get('config_file_path'):
    logger.info(f"配置文件: {config_info['config_file_path']}")


def is_running_under_gunicorn():
    """
    检测是否在gunicorn环境中运行
    """
    return "gunicorn" in os.environ.get("SERVER_SOFTWARE", "") or \
           "GUNICORN_CMD_ARGS" in os.environ or \
           hasattr(sys.modules.get("__main__", None), "__file__") and \
           "gunicorn" in str(sys.modules["__main__"].__file__)


# 检查函数已在导入语句中引入


def main():
    """
    启动生产服务器主函数
    支持直接运行和gunicorn启动两种模式
    """
    # 环境检查
    if not check_python_version():
        sys.exit(1)

    if not check_dependencies():
        sys.exit(1)

    # 检查凭证（仅警告，不退出）
    check_credentials()

    # 检查是否在gunicorn环境中运行
    if is_running_under_gunicorn():
        logger.info("检测到gunicorn环境，跳过直接启动")
        return

    # 从配置管理器获取服务器配置
    server_config = config_manager.get_server_config()
    performance_config = config_manager.get_performance_config()

    # 服务器配置（配置管理器已处理优先级：环境变量 > 配置文件 > 默认值）
    host = config_manager.get('host')
    port = config_manager.get('port')

    # 性能配置
    threads = config_manager.get('threads')
    connection_limit = config_manager.get('connection_limit')
    cleanup_interval = config_manager.get('cleanup_interval')
    channel_timeout = config_manager.get('channel_timeout')

    # Uvicorn服务器配置（从配置管理器获取）
    workers = config_manager.get('workers')  # 工作进程数
    timeout = config_manager.get('timeout')  # 请求超时时间

    # 根据环境调整启动信息详细程度
    if environment == 'development':
        # 开发环境：简化启动信息
        logger.info("AI Hub开发服务器启动中...")
        logger.info(f"环境: {environment} | 地址: {host}:{port}")
        logger.info(f"日志级别: {logging_config.get('log_level_name', 'INFO')}")
    else:
        # 生产环境：详细启动信息
        logger.info("="*60)
        logger.info("AI Hub服务器启动中...")
        logger.info(f"环境: {environment}")
        logger.info(f"监听地址: {host}:{port}")
        logger.info(f"工作线程: {threads}")
        logger.info(f"连接限制: {connection_limit}")
        logger.info(f"清理间隔: {cleanup_interval}s")
        logger.info(f"通道超时: {channel_timeout}s")
        logger.info(f"日志目录: {logging_config.get('log_dir', 'logs')}")
        logger.info(f"日志级别: {logging_config.get('log_level_name', 'INFO')}")
        logger.info(f"日志保留: {logging_config.get('backup_count', 30)}天")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info("="*60)

    try:
        # 创建FastAPI应用
        logger.info("正在初始化FastAPI应用...")
        app = create_app()
        logger.info("FastAPI应用初始化完成")

        # 根据环境配置Uvicorn服务器参数
        if environment == 'development':
            # 开发环境：启用热重载和详细日志
            logger.info("正在启动开发服务器（支持热重载）...")
            uvicorn.run(
                "app.app:create_app",  # 使用字符串路径支持热重载
                host=host,
                port=port,
                workers=workers,  # 使用配置管理器中的workers值
                timeout_keep_alive=timeout,  # 使用配置管理器中的timeout值
                reload=True,  # 启用热重载
                reload_dirs=["app"],  # 监控app目录变化
                log_level="debug",  # 开发环境使用debug级别
                access_log=True,
                server_header=False,
                date_header=False,
                factory=True  # 支持工厂函数
            )
        else:
            # 生产环境：使用字符串路径支持多个worker
            logger.info("正在启动Uvicorn ASGI服务器...")
            uvicorn.run(
                "app.app:create_app",  # 使用字符串路径支持多个worker
                host=host,
                port=port,
                workers=workers,  # 使用配置管理器中的workers值
                timeout_keep_alive=timeout,  # 使用配置管理器中的timeout值
                log_level="info",
                access_log=True,
                server_header=False,
                date_header=False,
                factory=True  # 支持工厂函数
            )
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        logger.info("服务器已安全关闭")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)


if __name__ == '__main__':
    main()
