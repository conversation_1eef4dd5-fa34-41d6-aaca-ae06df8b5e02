import io
import os
import time
import logging
import requests
from typing import Dict, Any, Optional
from PIL import Image
from docx.shared import Inches, RGBColor
from pydantic import ValidationError
from fastapi.responses import JSONResponse

from app.core.config import settings
from app.models.requests import WsinstRequest
from app.models.responses import WsinstResponse
from app.utils.file_utils import get_file_url
from app.utils.http import create_success_response, create_error_response
from app.gendoc.pdf import process_pdf_file, calculate_image_display_width


logger = logging.getLogger(__name__)

def get_file_mime_type(file_info: dict) -> str:
    """
    获取文件的MIME类型

    Args:
        file_info: 文件信息字典

    Returns:
        str: MIME类型，如果未指定则返回空字符串
    """
    return file_info.get('mimeType', '').lower()


def is_image_file(mime_type: str) -> bool:
    """
    判断是否为图片文件

    Args:
        mime_type: MIME类型

    Returns:
        bool: 是否为图片文件
    """
    image_types = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        'image/bmp', 'image/webp', 'image/tiff', 'image/svg+xml'
    ]
    return mime_type in image_types


def is_pdf_file(mime_type: str) -> bool:
    """
    判断是否为PDF文件

    Args:
        mime_type: MIME类型

    Returns:
        bool: 是否为PDF文件
    """
    return mime_type == 'application/pdf'


def process_image_file(doc, file_stream: io.BytesIO, file_info: dict):
    """
    处理图片文件

    Args:
        doc: Word文档对象
        file_stream: 文件字节流
        file_info: 文件信息字典
    """
    # 打开图片获取宽高信息
    with Image.open(file_stream) as img:
        display_width = calculate_image_display_width(img)

    # 重置文件指针到开始位置
    file_stream.seek(0)

    # 添加图片到文档
    doc.add_picture(file_stream, width=display_width)


def handle_wsinst_request(request: dict) -> JSONResponse:
    """处理节水型单位文档生成请求

    Args:
        request: 包含任务信息和文档内容的字典

    Returns:
        JSONResponse: FastAPI响应对象，包含JSON格式的结果
    """
    try:
        # 验证请求参数
        if 'Contents' not in request: # 修改：允许Contents为空
            request['Contents'] = ''

        if 'TaskKey' not in request or not request['TaskKey']:
            raise ValueError("TaskKey不能为空")

        if 'Files' not in request:
            request['Files'] = []

        # 记录开始时间
        start_time = time.time()

        # 生成文档
        doc_stream = generate_wsinst_document(request['Contents'], request['Files'])

        # 计算任务持续时间
        duration = time.time() - start_time

        # 复制生成的文件到静态文件目录
        base_dir = settings.STATIC_FILE_DIR
        file_name = f"{request['TaskKey']}.docx"
        file_path = os.path.join(base_dir, file_name)

        # 确保目录存在
        os.makedirs(base_dir, exist_ok=True)

        # 保存文件
        with open(file_path, "wb") as f:
            f.write(doc_stream.getvalue())

        # 生成文件URL
        file_url = get_file_url(file_name)

        # 构建响应数据
        response_data = {
            "TaskKey": request['TaskKey'],
            "RuntimeStatus": "SUCCESS",  # 修改为大写
            "TaskStatus": "COMPLETED",  # 修改为大写
            "Duration": duration
        }

        # 如果生成了文件访问地址，添加到响应数据中
        if file_url:
            response_data["FileUrl"] = file_url

        # 使用标准化成功响应格式
        success_response = create_success_response(
            response_data,
            "节水型单位文档生成成功"
        )

        # 返回JSON响应
        return JSONResponse(
            content=success_response,
            media_type='application/json; charset=utf-8'
        )

    except ValidationError as e:
        logger.error(f"请求参数验证失败: {e}")
        error_response = create_error_response(f"请求参数验证失败: {str(e)}", 400)
        return JSONResponse(
            content=error_response,
            media_type='application/json; charset=utf-8',
            status_code=400
        )
    except Exception as e:
        logger.error(f"处理节水型单位文档生成请求时发生错误: {e}")
        error_response = create_error_response(f"处理请求时发生错误: {str(e)}", 500)
        return JSONResponse(
            content=error_response,
            media_type='application/json; charset=utf-8',
            status_code=500
        )


def generate_wsinst_document(content: str, files: Optional[list] = None) -> io.BytesIO:
    """生成节水型单位文档

    Args:
        content: 文档内容
        files: 附件列表

    Returns:
        io.BytesIO: 包含文档内容的字节流
    """
    from docx import Document
    from app.gendoc.utils import init_doc_orientation, add_custom_simple_paragraph, add_custom_heading

    # 创建文档对象
    doc = Document()

    # 设置文档方向为纵向
    doc = init_doc_orientation(doc, {'orient': 'portrait'})

    # 添加第一部分：Content内容，使用宋体五号字
    # 五号字对应的字号是10.5
    if content:
        # 直接添加内容段落
        para_info = {
            "content": content,
            "fontName": "SimSun",  # 宋体
            "fontSize": 10.5,      # 五号字
            "fontBold": False,
            "fontItalic": False,
            "fontColor": "000000",  # 黑色
            "align": "left",
            "leftIndent": 2,
            "lineSpacing": 1.5,
            "spaceBefore": 0.5,
            "spaceAfter": 0.5
        }
        add_custom_simple_paragraph(doc, para_info)

    # 添加第二部分：Files内容
    if files and len(files) > 0:
        # 添加二级标题"附件"
        heading_info = {
            "content": "附件",
            "level": 2,
            "fontName": "SimHei",
            "fontSize": 16,
            "fontBold": True,
            "fontItalic": False,
            "fontColor": "000000"  # 黑色
        }
        add_custom_heading(doc, heading_info)

        # 为每个文件添加三级标题和内容
        for file_info in files:
            # 检查file_info是否为字典且包含name和url字段
            if isinstance(file_info, dict) and 'name' in file_info and 'url' in file_info:
                # 获取文件的MIME类型
                mime_type = get_file_mime_type(file_info)

                # 只处理图片和PDF文件，跳过其他类型
                if not (is_image_file(mime_type) or is_pdf_file(mime_type)):
                    logger.info(f"跳过不支持的文件类型: {file_info['name']} (MIME: {mime_type})")
                    continue

                # 添加三级标题，标题内容为Files.name
                file_heading_info = {
                    "content": file_info['name'],
                    "level": 3,
                    "fontName": "SimHei",
                    "fontSize": 14,
                    "fontBold": True,
                    "fontItalic": False,
                    "fontColor": "000000"  # 黑色
                }
                add_custom_heading(doc, file_heading_info)

                try:
                    # 下载文件
                    response = requests.get(file_info['url'], stream=True)
                    response.raise_for_status()
                    file_stream = io.BytesIO(response.content)

                    if is_image_file(mime_type):
                        # 处理图片文件
                        process_image_file(doc, file_stream, file_info)
                    elif is_pdf_file(mime_type):
                        # 处理PDF文件
                        process_pdf_file(doc, file_stream, file_info)

                except Exception as e:
                    logger.error(f"无法处理文件 {file_info['url']}: {e}")
                    # 添加错误提示
                    error_para = doc.add_paragraph()
                    error_run = error_para.add_run(f"[文件无法加载: {file_info['url']}]")
                    error_run.font.color.rgb = RGBColor(255, 0, 0)  # 红色错误文本

    # 保存文档到字节流
    output_stream = io.BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)

    return output_stream
