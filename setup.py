# -*- coding: utf-8 -*-
"""
AI Hub服务器安装脚本
"""

import os
from setuptools import setup, find_packages

# 读取README文件
README_PATH = os.path.join(os.path.dirname(__file__), 'README.md')
if os.path.exists(README_PATH):
    with open(README_PATH, 'r', encoding='utf-8') as f:
        long_description = f.read()
else:
    long_description = 'AI Hub服务器'

# 读取requirements.txt
REQUIREMENTS_PATH = os.path.join(os.path.dirname(__file__), 'requirements.txt')
if os.path.exists(REQUIREMENTS_PATH):
    with open(REQUIREMENTS_PATH, 'r', encoding='utf-8') as f:
        requirements = []
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                requirements.append(line)
else:
    requirements = [
        'Flask>=3.0.0',
        'waitress>=2.1.2',
        'alibabacloud_tea_openapi>=0.3.15,<1.0.0',
        'alibabacloud_tea_console>=0.0.1,<1.0.0',
        'alibabacloud_openapi_util>=0.2.2,<1.0.0',
        'alibabacloud_tea_util>=0.3.13,<1.0.0',
        'alibabacloud_credentials>=1.0.2,<2.0.0',
        'requests>=2.31.0',
        'Pillow>=11.3.0',
        'python-docx>=1.2.0',
    ]

setup(
    name='aihub-server',
    version='1.0.0',
    description='AI Hub服务器',
    long_description=long_description,
    long_description_content_type='text/markdown',
    author='AI Assistant',
    author_email='',
    url='',
    license='MIT',
    packages=find_packages(),
    include_package_data=True,
    python_requires='>=3.10',
    install_requires=requirements,
    entry_points={
        'console_scripts': [
            'aihub-server=app.main:main',
            'aihub-cli=app.cli_tool:main',
        ],
    },
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Programming Language :: Python :: 3.12',
        'Topic :: Internet :: WWW/HTTP :: HTTP Servers',
        'Topic :: Multimedia :: Sound/Audio :: Speech',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ],
    keywords=['aihub', 'aliyun', 'tingwu', 'speech', 'transcription', 'audio', 'flask', 'http', 'server'],
    project_urls={
        'Documentation': 'https://help.aliyun.com/document_detail/378659.html',
        'Source': '',
        'Tracker': '',
    },
)