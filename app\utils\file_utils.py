# -*- coding: utf-8 -*-
"""
文件工具函数

提供文件相关的工具函数，如获取文件URL、检查文件是否存在等。
"""
import os
from app.core.config import settings


def get_file_url(file_name: str) -> str:
    """
    生成文件的访问URL

    Args:
        file_name: 文件名

    Returns:
        str: 文件的完整URL
    """
    # 构建文件的完整URL
    # 假设静态文件通过settings.STATIC_URL_PREFIX访问
    # 这里根据项目实际情况进行调整
    if hasattr(settings, 'STATIC_URL_PREFIX'):
        static_url = settings.STATIC_URL_PREFIX
    else:
        # 如果没有配置STATIC_URL_PREFIX，则使用默认值
        static_url = '/static'

    # 确保URL前缀以/开头
    if not static_url.startswith('/'):
        static_url = f'/{static_url}'

    # 确保URL前缀不以/结尾
    if static_url.endswith('/'):
        static_url = static_url[:-1]

    # 构建完整的文件URL
    # 这里假设API_HOST和API_PORT在settings中配置
    if hasattr(settings, 'API_HOST') and hasattr(settings, 'API_PORT'):
        # 如果有配置API_HOST和API_PORT，则使用完整的URL
        protocol = 'http' if settings.API_PORT != 443 else 'https'
        file_url = f"{protocol}://{settings.API_HOST}:{settings.API_PORT}{static_url}/{file_name}"
    else:
        # 否则返回相对路径
        file_url = f"{static_url}/{file_name}"

    return file_url
